{"name": "book-bot", "version": "1.0.0", "description": "", "scripts": {"create-env": "PROJECT=turnero STACK=MyStack zx createEnv.mjs", "start": "sst start --increase-timeout", "deploy": "sst deploy", "remove": "sst remove", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .ts --max-warnings=0", "lint-and-fix": "eslint . --ext .ts --max-warnings=0 --fix", "prettier-check": "prettier --config .prettierrc '**/*.ts' '**/*.js' --check", "prettier-format": "prettier --config .prettierrc '**/*.ts' '**/*.js' --write", "test-unit": "TEST_MODE=unit vitest --run", "test-unit-noBO": "TEST_MODE=unitNoBO vitest --run", "test-unit-ui": "TEST_MODE=unit vitest --ui --coverage", "test-e2e": "TEST_MODE=e2e vitest --run", "test-all": "npm run test-unit && npm run test-e2e", "check-node-version": "node check-node-version.js", "preinstall": "npm run check-node-version", "migrate": "npx sequelize-cli db:migrate", "migrate-analytics": "DB_TYPE=analytics npx sequelize-cli db:migrate", "migrate-rm-rows-all": "src/shared/infra/database/sequelize/migrations/rmRowsAll.sh", "migrate-rm-meta-seed": "src/shared/infra/database/sequelize/migrations/rmMetaSeed.sh", "migrate-rm-meta-all": "src/shared/infra/database/sequelize/migrations/rmMetaAll.sh", "migrate-analytics-rm-meta-all": "src/shared/infra/database/sequelize_analytics/migrations/rmMetaAll.sh", "migrate-reload-seed": "npm run migrate-rm-rows-all && npm run migrate-rm-meta-seed && npm run migrate", "migrate-rm-tables-all": "src/shared/infra/database/sequelize/migrations/rmTablesAll.sh", "migrate-analytics-rm-tables-all": "src/shared/infra/database/sequelize_analytics/migrations/rmTablesAll.sh", "migrate-restart": "npm run migrate-rm-tables-all && npm run migrate-rm-meta-all && npm run migrate", "migrate-analytics-restart": "npm run migrate-analytics-rm-tables-all && npm run migrate-analytics-rm-meta-all && npm run migrate-analytics", "migrate-to": "npx sequelize-cli db:migrate --to", "migrate-toEXAMPLE": "npm run migrate-to -- 027-create-calendars.js", "migrate-undo": "npx sequelize-cli db:migrate:undo", "migrate-analytics-undo": "DB_TYPE=analytics npx sequelize-cli db:migrate:undo", "migrate-undo-all": "npx sequelize-cli db:migrate:undo:all", "migrate-status": "npx sequelize-cli db:migrate:status", "migrate-analytics-status": "DB_TYPE=analytics npx sequelize-cli db:migrate:status"}, "author": "", "license": "ISC", "devDependencies": {"@aws-appsync/utils": "^1.6.0", "@tsconfig/node20": "^20.1.2", "@types/async-retry": "^1.4.8", "@types/aws-lambda": "^8.10.131", "@types/chance": "^1.1.6", "@types/html-to-text": "^9.0.4", "@types/json-stringify-safe": "^5.0.3", "@types/luxon": "^3.4.2", "@types/node-fetch": "^2.6.11", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "aws-cdk-lib": "2.110.1", "chance": "^1.1.11", "constructs": "^10.3.0", "dotenv": "^16.3.2", "esbuild": "^0.19.11", "eslint": "^8.55.0", "expect-type": "^0.19.0", "graphql-tag": "^2.12.6", "node-fetch": "^2.7.0", "prettier": "^3.1.0", "semver": "^7.5.4", "sequelize-cli": "^6.6.2", "sst": "^2.39.7", "ts-node": "^10.9.2", "tsc-alias": "^1.8.8", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2", "vitest": "^2.0.5", "zx": "^7.2.3"}, "dependencies": {"@aws-sdk/client-sns": "^3.817.0", "@googleapis/calendar": "^9.7.0", "exponential-backoff": "^3.1.2", "google-auth-library": "^9.7.0", "html-to-text": "^9.0.5", "json-stringify-safe": "^5.0.1", "libphonenumber-js": "^1.12.8", "luxon": "^3.4.4", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.36.0", "uuid": "^9.0.1"}}