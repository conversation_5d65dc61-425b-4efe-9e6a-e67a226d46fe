// When generating the package "s4nt14go-book-backend-dev" in folder "pack/dev" included in "front-dev.ts":
// import { getCalendarCreds as _getCalendarCreds } from '@shared/infra/iac/helpers';
// ...I got the following error when importing it on the frontend: "Invariant violation: new TextEncoder().encode( instanceof Uint8Array is incorrectly false)"
// ...coming from "esbuild"

// So in order to export getCalendarCreds and use it on the frontend, I had to place:
// import * as esbuild from 'esbuild';
// ...in a file other than "@shared/infra/iac/helpers".

import { Code } from 'aws-cdk-lib/aws-appsync';
import * as esbuild from 'esbuild';

const bundleAppSyncResolver = (entryPoint: string): Code => {
  const result = esbuild.buildSync({
    entryPoints: [entryPoint],
    external: ['@aws-appsync/utils'],
    bundle: true,
    write: false,
    platform: 'node',
    target: 'esnext',
    format: 'esm',
    sourcemap: 'inline',
    sourcesContent: false,
  });

  return Code.fromInline(result.outputFiles[0].text);
};

export default bundleAppSyncResolver;
