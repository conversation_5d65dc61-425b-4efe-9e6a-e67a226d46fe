import { SSM } from 'aws-sdk';

export async function getCalendarCreds(app: { name: string; stage: string }) {
  const ssm = new SSM(); // If this line is put outside the function, aws region isn't picked up.
  let ssmGetResponse;
  try {
    ssmGetResponse = await ssm
      .getParameter({ Name: `/${app.name}/${app.stage}/calendar` })
      .promise();
  } catch (e) {
    console.log(
      `\nMake sure you have parameter /${app.name}/${app.stage}/calendar in AWS account's Parameter Store with your Google Calendar data following this structure: $calendar_clientEmail,$calendar_clientKey\n`,
    );
    throw e;
  }

  if (
    !ssmGetResponse ||
    !ssmGetResponse.Parameter ||
    !ssmGetResponse.Parameter.Value
  ) {
    throw Error('No Google Calendar data found in SSM');
  }
  const calendar = ssmGetResponse.Parameter.Value;
  const [calendar_clientEmail, calendar_clientKey] = calendar.split(',');
  return {
    calendar_clientEmail,
    calendar_clientKey,
  };
}

export async function getDbCreds(app: { name: string; stage: string }) {
  const ssm = new SSM(); // If this line is put outside the function, aws region isn't picked up.
  let ssmGetResponse, ssmGetResponseAnalytics;
  try {
    ssmGetResponse = await ssm
      .getParameter({ Name: `/${app.name}/${app.stage}/cockroach` })
      .promise();
    ssmGetResponseAnalytics = await ssm
      .getParameter({ Name: `/${app.name}/${app.stage}/cockroach_analytics` })
      .promise();
  } catch (e) {
    console.log(
      `\nMake sure you have parameter /${app.name}/${app.stage}/cockroach in AWS account's Parameter Store with your CockroachDB data following this structure: $username,$password,$database,$host,$dialect,$port,$cluster\n
      And also /${app.name}/${app.stage}/cockroach_analytics\n`,
    );
    throw e;
  }

  if (
    !ssmGetResponse ||
    !ssmGetResponse.Parameter ||
    !ssmGetResponse.Parameter.Value ||
    !ssmGetResponseAnalytics ||
    !ssmGetResponseAnalytics.Parameter ||
    !ssmGetResponseAnalytics.Parameter.Value
  ) {
    throw Error('No cockroach data found in SSM');
  }
  const cockroach = ssmGetResponse.Parameter.Value;
  const [username, password, database, host, dialect, port, cluster] =
    cockroach.split(',');
  const cockroach_analytics = ssmGetResponseAnalytics.Parameter.Value;
  const [username_a, password_a, database_a, host_a, dialect_a, port_a, cluster_a] =
    cockroach_analytics.split(',');
  return {
    app: {
      COCKROACH_username: username,
      COCKROACH_password: password,
      COCKROACH_database: database,
      COCKROACH_host: host,
      COCKROACH_dialect: dialect,
      COCKROACH_port: port,
      COCKROACH_cluster: cluster,
    },
    analytics: {
      COCKROACH_ANALYTICS_username: username_a,
      COCKROACH_ANALYTICS_password: password_a,
      COCKROACH_ANALYTICS_database: database_a,
      COCKROACH_ANALYTICS_host: host_a,
      COCKROACH_ANALYTICS_dialect: dialect_a,
      COCKROACH_ANALYTICS_port: port_a,
      COCKROACH_ANALYTICS_cluster: cluster_a,
    },
  };
}
