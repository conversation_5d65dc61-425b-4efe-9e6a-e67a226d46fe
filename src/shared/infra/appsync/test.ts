import { N0 } from '@shared/core/N0';
import { PositiveInt } from '@shared/core/PositiveInt';
import { MAX_GRAPHQL_INT } from '@shared/infra/appsync/utils';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

export function createN0(value?: number) {
  const v = value !== undefined ? value : chance.integer({ min: 0, max: MAX_GRAPHQL_INT });
  return N0.create({ value: v }).value;
}
export function createPositiveInt(value?: number) {
  const v = value !== undefined ? value : chance.integer({ min: 1, max: MAX_GRAPHQL_INT });
  return PositiveInt.create({ value: v }).value;
}
