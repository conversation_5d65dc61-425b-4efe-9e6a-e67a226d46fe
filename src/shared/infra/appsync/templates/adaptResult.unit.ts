import { expect, it, describe } from 'vitest';
import { getAppsyncCtx } from '@shared/utils/test';
import { response } from './adaptResult';

describe(`when ctx.result.result it's`, () => {
  it(`an object`, () => {
    const input = getAppsyncCtx(
      {},
      {
        result: {
          some_data: 10,
        },
        time: 'some time',
      },
    );
    const result = response(input);

    expect(result).toEqual({
      result: {
        some_data: 10,
      },
      time: 'some time',
    });
  });

  it.each([null, undefined, '1', 1, 0, true, false])(
    `a primitive (%s), it should return { result: <primitive> }`,
    (value) => {
      const input = getAppsyncCtx(
        {},
        {
          result: value,
          time: 'some time',
        },
      );
      const result = response(input);

      expect(result).toEqual({
        result: value,
        time: 'some time',
      });
    },
  );

  it.each([
    { array: [] },
    { array: [1, '2', true, new Date()] },
    { array: [{}] },
    { array: [{ p: 1 }] },
  ])(
    `an array %s, it should return { result: <array> }`,
    (input: Record<string, unknown>) => {
      const ctx = getAppsyncCtx(
        {},
        {
          result: input.array,
          time: 'some time',
        },
      );
      const result = response(ctx);

      expect(result).toEqual({
        result: input.array,
        time: 'some time',
      });
    },
  );
});
