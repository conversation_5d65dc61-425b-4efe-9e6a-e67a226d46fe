import { Context, util } from '@aws-appsync/utils';
// Si da error al deployar, se puede editar desde la consola de AWS AppSync (cuenta "dev") y dice cuáles son los errores:
// https://us-east-1.console.aws.amazon.com/appsync/home?region=us-east-1#/esc3vgppzfby5btkyube5c4ebi/v1/schema
// ...click en "Pipeline" de la query

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function request(_ctx: Context) {
  return {};
}

export function response(ctx: Context) {
  // Keep error detection in synchro with src/shared/core/Envelope.ts: as Envelope.errorType takes 'MultipleErrors' or BaseError.constructor.name (if we have just one error) => errors will always be detected by AppSync
  if (ctx.result.errorType) {
    return util.error(ctx.result.errorMessage, ctx.result.errorType, null, {
      time: ctx.result.time,
      errors: ctx.result.errors,
    });
  }

  return ctx.result;
}
