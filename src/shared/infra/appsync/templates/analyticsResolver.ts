// This resolver immediately returns 202 and then processes the analytics event asynchronously
import { Context } from '@aws-appsync/utils';

export function request(ctx: Context) {
  // Return the event data to be passed to the Lambda function
  return {
    payload: ctx.arguments,
    operation: 'Invoke',
    invocationType: 'Event', // This makes the Lambda invocation asynchronous
  };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function response(_ctx: Context) {
  // Immediately return a 202 Accepted status
  return {
    statusCode: 202,
    message: 'Event received and being processed',
  };
}