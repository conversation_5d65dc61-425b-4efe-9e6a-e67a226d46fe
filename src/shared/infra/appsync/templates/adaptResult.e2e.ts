import { expect, it } from 'vitest';
import { AppSyncClient } from '../AppSyncClient';
import { dateFormat } from '@shared/utils/test';
import { createReservationOption } from '../../../../modules/reservation/utils/testExternal';
import { Request as RequestForCreateReservationOption } from '../../../../modules/reservation/useCases/createReservationOption/CreateReservationOptionDTOs';
import { getLng } from '@shared/utils/utils';
import {
  createReservationOptionQuery,
} from '../../../../modules/reservation/utils/fragments';

const appsync = new AppSyncClient();

it('should return error fields: message, errorType and errorInfo', async () => {
  const response = await appsync.send<RequestForCreateReservationOption>({
    query: createReservationOptionQuery,
    variables: {
      ...createReservationOption().dto,
      lng: getLng(),
      name: '', // invalid name
    },
  });

  expect(response.status).toBe(200);
  const json = await response.json();

  expect(json).toMatchObject({
    data: null,
    errors: [
      {
        errorType: 'NameErrors.TooShort',
        errorInfo: {
          time: expect.stringMatching(dateFormat),
          errors: [
            {
              message: expect.any(String),
              status: 400,
              type: 'NameErrors.TooShort',
            },
          ],
        },
        message: expect.not.stringContaining(
          'A custom error was thrown from a mapping template.',
        ), // if <message> is not a string in $util.error(<message>, ...), AppSync returns this text
      },
    ],
  });
});
