// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../models');
import { EntityID } from '@shared/core/domain/EntityID';
import { DomainEvents } from '@shared/core/domain/DomainEvents';

const dispatchEventsCallback = async (
  model: any, // eslint-disable-line @typescript-eslint/no-explicit-any
  options: any, // eslint-disable-line @typescript-eslint/no-explicit-any
  primaryKeyField: string,
  hook: string,
) => {
  let id = model[primaryKeyField];
  let log = `Hooking ${hook} for ${model.constructor.name} ${id}`;
  if (hook.includes('Bulk')) {
    id = model.attributes[primaryKeyField];
    log = `Hooking ${hook} for ${id}`;
  }
  console.log(log);
  /*if (model.constructor.name === 'transaction') {
    console.log(
      'Transaction is an internal entity to Account aggregate, so use account id to check for events'
    );
    id = model['accountId'];
  }*/
  await DomainEvents.dispatchEventsForAggregate(new EntityID(id));
};

export default function () {
  // const { User, Account, Transaction } = models;
  const { CustomerUser, BusinessUser, ManuallyCreatedCustomer, Reservation } = models;

  const hooks = [
    'afterCreate',
    'afterDestroy',
    'afterUpdate',
    'afterBulkUpdate',
    'afterSave',
    'afterUpsert',
  ];
  hooks.map((h) => {
    CustomerUser.addHook(h, (m: unknown, options: unknown) =>
      dispatchEventsCallback(m, options, 'id', h),
    );
    BusinessUser.addHook(h, (m: unknown, options: unknown) =>
      dispatchEventsCallback(m, options, 'id', h),
    );
    ManuallyCreatedCustomer.addHook(h, (m: unknown, options: unknown) =>
      dispatchEventsCallback(m, options, 'id', h),
    );
    Reservation.addHook(h, (m: unknown, options: unknown) =>
      dispatchEventsCallback(m, options, 'id', h),
    );
  });

  console.log('[Hooks]: Sequelize hooks setup.');
}
