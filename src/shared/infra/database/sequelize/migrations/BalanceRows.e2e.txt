Keep track of BalanceRowsInDb used in e2e tests to prevent test collisions:
00:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
01:
  userId: 01a82ed0-c39a-11ee-8adf-3f66c88fcbdd x
02:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
03:
  userId: 01a82ed0-c39a-11ee-8adf-3f66c88fcbdd x
04:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
05:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
06:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
07:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
08:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
09:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
10:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
11:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
12:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
13:
  userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd x
14:
  userId: 07a82ed0-c39a-11ee-8adf-3f66c88fcbdd x
15:
  userId: 02a82ed0-c39a-11ee-8adf-3f66c88fcbdd x
16:
  userId: 06a82ed0-c39a-11ee-8adf-3f66c88fcbdd x

userId: 00ad8920-c36d-11ee-8adf-3f66c88fcbdd
  src/modules/reservation/useCases/createReservationByBusiness/CreateReservationByBusiness.e2e.ts
userId: 02a82ed0-c39a-11ee-8adf-3f66c88fcbdd
  src/modules/reservation/useCases/createReservation/CreateReservation.e2e.ts
userId: 01a82ed0-c39a-11ee-8adf-3f66c88fcbdd
  src/modules/balance/useCases/moveInvolvingReservation/MoveInvolvingReservation.e2e.ts
userId: 05a82ed0-c39a-11ee-8adf-3f66c88fcbdd
  src/modules/reservation/useCases/getAvailabilityOfReservationOptions/GetAvailabilityOfReservationOptions.e2e.ts uses calendars "availability 5" and "reservation 5", which are associated to 05a82ed0-c39a-11ee-8adf-3f66c88fcbdd
userId: 06a82ed0-c39a-11ee-8adf-3f66c88fcbdd
  src/modules/reservation/useCases/cancelReservation/CancelReservation.e2e.ts
userId: 07a82ed0-c39a-11ee-8adf-3f66c88fcbdd
  src/modules/reservation/useCases/cancelReservationByBusiness/CancelReservationByBusiness.e2e.ts