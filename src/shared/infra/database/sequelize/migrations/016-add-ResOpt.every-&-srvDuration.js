'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'reservation_options',
        'every',
        {
          type: DataTypes.INTEGER,
          allowNull: true, // set to false in next migration
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'reservation_options',
        'srvDuration',
        {
          type: DataTypes.INTEGER,
          allowNull: true, // set to false in next migration
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('reservation_options', 'every', {
        transaction,
      });
      await queryInterface.removeColumn('reservation_options', 'srvDuration', {
        transaction,
      });

      await queryInterface.changeColumn(
        'reservation_options',
        'srvEvery',
        {
          type: DataTypes.INTEGER,
          allowNull: false,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
