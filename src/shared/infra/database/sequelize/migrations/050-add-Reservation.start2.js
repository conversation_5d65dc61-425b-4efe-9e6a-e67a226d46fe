'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.addColumn(
        'reservations',
        'start2',
        {
          type: DataTypes.DATE,
          // allowNull: false, set below
        },
        { transaction },
      );

      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM reservations;
      `,
        { transaction },
      );
      for (const record of records[0]) {
        const { id, start } = record;
        const start2value = new Date(start).toJSON();

        await queryInterface.sequelize.query(
          `
          UPDATE reservations SET "start2" = '${start2value}'
          WHERE "id" = '${id}';
        `,
          { transaction },
        );
      }

      await queryInterface.changeColumn(
        'reservations',
        'start2',
        {
          type: DataTypes.DATE,
          allowNull: false,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.removeColumn(
        'reservations',
        'start2',
        {
          transaction,
        },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};