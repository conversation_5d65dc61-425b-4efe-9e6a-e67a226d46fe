'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    await queryInterface.createTable(
      'gs_for_calendars',

      {
        id: {
          type: DataTypes.UUID,
          allowNull: false,
          primaryKey: true,
        },
        resLimMaxDaysAhead: {
          type: DataTypes.INTEGER,
          allowNull: false,
        },
        resLimMinTimeBeforeService: {
          type: DataTypes.INTEGER,
          allowNull: false,
        },
        created_at: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updated_at: {
          type: DataTypes.DATE,
          allowNull: false,
        },
      },
    );
  },
  async down(queryInterface) {
    await queryInterface.dropTable('gs_for_calendars');
  },
};
