'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    await queryInterface.createTable('location_versions', {
      _id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      name: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
      },
      identifier: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      showInfoWhenReserving: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
      },
      version: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      vcreated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      vupdated_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('location_versions');
  },
};
