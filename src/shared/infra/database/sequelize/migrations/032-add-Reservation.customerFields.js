'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'reservations',
        'customerFirstName',
        {
          type: DataTypes.TEXT,
        },
        { transaction }
      );
      await queryInterface.addColumn(
        'reservations',
        'customerLastName',
        {
          type: DataTypes.TEXT,
        },
        { transaction }
      );
      await queryInterface.addColumn(
        'reservations',
        'customerPhone',
        {
          type: DataTypes.TEXT,
        },
        { transaction }
      );
      await queryInterface.addColumn(
        'reservations',
        'customerEmail',
        {
          type: DataTypes.TEXT,
        },
        { transaction }
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(
        'reservations',
        'customerFirstName',
        { transaction },
      );
      await queryInterface.removeColumn(
        'reservations',
        'customerLastName',
        { transaction },
      );
      await queryInterface.removeColumn(
        'reservations',
        'customerPhone',
        { transaction },
      );
      await queryInterface.removeColumn(
        'reservations',
        'customerEmail',
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};