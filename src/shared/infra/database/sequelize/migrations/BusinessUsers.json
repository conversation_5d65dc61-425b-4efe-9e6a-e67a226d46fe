{"BusinessUsersInDb": [{"id": "00ad8920-c36d-11ee-8adf-3f66c88fcbdd", "firstName": "<PERSON>", "lastName": "<PERSON>", "lng": "en", "email": "<EMAIL>", "phone": "+549345123411", "timezone": "America/Cordoba", "phoneVerificationStatus": "SUCCESS", "phoneVerificationAttempts": ["2024-02-26T00:00:00.000Z"], "phoneVerificationBlockedUntil": null, "phoneVerificationNewPhone": "+549345123411", "phoneVerificationCode": 1234, "phoneVerificationCodeExpiresAt": "2024-02-26T00:01:00.000Z", "created_at": "2024-02-24T00:00:00.000Z", "updated_at": "2024-02-25T00:00:00.000Z", "deleted_at": null}, {"id": "01a82ed0-c39a-11ee-8adf-3f66c88fcbdd", "firstName": null, "lastName": null, "lng": "es", "email": "<EMAIL>", "phone": "+549345123422", "timezone": "America/New_York", "phoneVerificationStatus": "INITIAL", "phoneVerificationAttempts": null, "phoneVerificationBlockedUntil": null, "phoneVerificationNewPhone": null, "phoneVerificationCode": null, "phoneVerificationCodeExpiresAt": null, "created_at": "2024-02-26T00:00:00.000Z", "updated_at": "2024-02-27T00:00:00.000Z", "deleted_at": null}, {"id": "02a82ed0-c39a-11ee-8adf-3f66c88fcbdd", "firstName": null, "lastName": "Magnum", "lng": "en", "email": "<EMAIL>", "phone": null, "timezone": "Africa/Cairo", "phoneVerificationStatus": "STARTED", "phoneVerificationAttempts": ["2024-02-26T00:00:00.000Z", "2024-02-26T01:00:00.000Z"], "phoneVerificationBlockedUntil": null, "phoneVerificationNewPhone": "+5493513881100", "phoneVerificationCode": 4502, "phoneVerificationCodeExpiresAt": "2024-02-28T01:01:00.000Z", "created_at": "2024-02-28T00:00:00.000Z", "updated_at": "2024-02-29T00:00:00.000Z", "deleted_at": null}, {"id": "03a82ed0-c39a-11ee-8adf-3f66c88fcbdd", "firstName": "<PERSON>", "lastName": "Princeton", "lng": "es", "email": "<EMAIL>", "phone": "+549345122222", "timezone": "Asia/Gaza", "phoneVerificationStatus": "SUCCESS", "phoneVerificationAttempts": ["2024-02-24T01:00:00.000Z", "2024-02-25T01:00:00.000Z", "2024-02-26T01:00:00.000Z"], "phoneVerificationBlockedUntil": "2024-02-27T01:00:00.000Z", "phoneVerificationNewPhone": "+549345122222", "phoneVerificationCode": 6239, "phoneVerificationCodeExpiresAt": "2024-02-26T01:01:00.000Z", "created_at": "2024-04-28T00:00:00.000Z", "updated_at": "2024-04-29T00:00:00.000Z", "deleted_at": "2024-04-30T00:00:00.000Z"}, {"id": "04a82ed0-c39a-11ee-8adf-3f66c88fcbdd", "firstName": "<PERSON><PERSON>lo en", "lastName": "<PERSON><PERSON><PERSON><PERSON> user", "lng": "en", "email": "<EMAIL>", "phone": "+549345122233", "timezone": "Asia/Gaza", "phoneVerificationStatus": "SUCCESS", "phoneVerificationAttempts": ["2024-02-24T01:00:00.000Z"], "phoneVerificationBlockedUntil": null, "phoneVerificationNewPhone": "+549345122233", "phoneVerificationCode": 4566, "phoneVerificationCodeExpiresAt": "2024-02-24T01:01:00.000Z", "created_at": "2024-04-28T00:00:00.000Z", "updated_at": "2024-04-29T00:00:00.000Z", "deleted_at": null}, {"id": "05a82ed0-c39a-11ee-8adf-3f66c88fcbdd", "firstName": "<PERSON>", "lastName": null, "lng": "en", "email": "<EMAIL>", "phone": "+549345122233", "timezone": "America/Cordoba", "phoneVerificationStatus": "CODE_EXPIRED", "phoneVerificationAttempts": ["2024-02-26T00:00:00.000Z", "2024-02-27T00:00:00.000Z"], "phoneVerificationBlockedUntil": null, "phoneVerificationNewPhone": "+5493513276611", "phoneVerificationCode": 8967, "phoneVerificationCodeExpiresAt": "2024-02-27T00:00:01.000Z", "created_at": "2024-04-28T00:00:00.000Z", "updated_at": "2024-04-29T00:00:00.000Z", "deleted_at": null}, {"id": "06a82ed0-c39a-11ee-8adf-3f66c88fcbdd", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Gutierrez", "lng": "es", "email": "<EMAIL>", "phone": "+549345122233", "timezone": "America/Cordoba", "phoneVerificationStatus": "INVALID_CODE", "phoneVerificationAttempts": ["2024-02-27T00:00:00.000Z"], "phoneVerificationBlockedUntil": null, "phoneVerificationNewPhone": "+5493513276688", "phoneVerificationCode": null, "phoneVerificationCodeExpiresAt": "2024-02-27T00:01:00.000Z", "created_at": "2024-04-28T00:00:00.000Z", "updated_at": "2024-04-29T00:00:00.000Z", "deleted_at": null}, {"id": "07a82ed0-c39a-11ee-8adf-3f66c88fcbdd", "firstName": "<PERSON>", "lastName": "<PERSON>", "lng": "es", "email": "<EMAIL>", "phone": "+549347462233", "timezone": "America/Cordoba", "phoneVerificationStatus": "STARTED", "phoneVerificationAttempts": ["2024-02-28T00:00:00.000Z"], "phoneVerificationBlockedUntil": null, "phoneVerificationNewPhone": "+5493513276600", "phoneVerificationCode": 5688, "phoneVerificationCodeExpiresAt": "2024-02-28T00:01:00.000Z", "created_at": "2024-04-30T00:00:00.000Z", "updated_at": "2024-04-30T00:00:00.000Z", "deleted_at": null}, {"id": "08ad8920-c36d-11ee-8adf-3f66c88fcbdd", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "lng": "en", "email": "<EMAIL>", "phone": null, "timezone": "America/Cordoba", "phoneVerificationStatus": "STARTED", "phoneVerificationAttempts": ["2024-03-28T00:00:00.000Z"], "phoneVerificationBlockedUntil": null, "phoneVerificationNewPhone": "+5493511116600", "phoneVerificationCode": 5118, "phoneVerificationCodeExpiresAt": "2024-03-28T00:01:00.000Z", "created_at": "2024-02-24T00:00:00.000Z", "updated_at": "2024-02-25T00:00:00.000Z", "deleted_at": null}, {"id": "09ad8920-c36d-11ee-8adf-3f66c88fcbdd", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Lape<PERSON>", "lng": "es", "email": "<EMAIL>", "phone": null, "timezone": "America/Cordoba", "phoneVerificationStatus": "INITIAL", "phoneVerificationAttempts": null, "phoneVerificationBlockedUntil": null, "phoneVerificationNewPhone": null, "phoneVerificationCode": null, "phoneVerificationCodeExpiresAt": null, "created_at": "2024-02-24T00:00:00.000Z", "updated_at": "2024-02-25T00:00:00.000Z", "deleted_at": null}]}