'use strict';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM balance_rows;
      `,
        { transaction },
      );
      for (const record of records[0]) {
        const { reservationId } = record;
        if (!reservationId) continue;
        const selected = await queryInterface.sequelize.query(
          `
        SELECT * FROM reservations WHERE id='${reservationId}';
      `,
          { transaction },
        );
        const reservation = selected[0][0];
        const { customerId } = reservation;

        await queryInterface.sequelize.query(
          `
          UPDATE balance_rows SET "customerId" = '${customerId}'
          WHERE "reservationId" = '${reservationId}';
        `,
          { transaction },
        );
      }

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      console.log('No down migration for this migration');

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};