'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.addColumn(
        'gs_for_reservations',
        'resLimMaxDaysAhead',
        {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 30,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'gs_for_reservations',
        'resLimMinTimeBeforeService',
        {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 1440,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.removeColumn(
        'gs_for_reservations',
        'resLimMaxDaysAhead',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'gs_for_reservations',
        'resLimMinTimeBeforeService',
        {
          transaction,
        },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};