import { vi, it } from 'vitest';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { up, down } = require('../901-seed');

it(`shouldn't run if STAGE is different from "dev"`, async () => {
  vi.stubEnv('STAGE', 'not dev');

  // Spy on methods of queryInterface
  const queryInterface = {
    sequelize: {
      transaction: () => {
        throw Error(`transaction shouldn't be called`);
      },
    },
    bulkDelete: () => {
      throw Error(`bulkDelete shouldn't be called`);
    },
    bulkInsert: () => {
      throw Error(`bulkInsert shouldn't be called`);
    },
  };
  const Sequelize = { JSON: vi.fn() };

  // Call up & down functions
  await up(queryInterface, Sequelize);
  await down(queryInterface, Sequelize);
});
