#!/bin/bash

# npm run migrate-rm-meta-seed

source .env
echo "Removing all rows from reservation_options, customer_users, business_users, manually_created_customers, reservations, calendars, gs_for_reservations, and balance_rows"
psql postgres://$COCKROACH_username:$<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_password@$COCKROACH_host:$COCKROACH_port/defaultdb << EOF
  DELETE FROM reservation_options;
  DELETE FROM customer_users;
  DELETE FROM business_users;
  DELETE FROM manually_created_customers;
  DELETE FROM reservations;
  DELETE FROM calendars;
  DELETE FROM gs_for_reservations;
  DELETE FROM balance_rows;
EOF