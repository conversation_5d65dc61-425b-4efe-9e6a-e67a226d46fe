'use strict';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('location_versions', 'vcreated_at', {
        transaction,
      });
      await queryInterface.removeColumn('location_versions', 'vupdated_at', {
        transaction,
      });

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const attributes = {
        type: DataTypes.DATE,
        allowNull: false,
      };
      await queryInterface.addColumn(
        'location_versions',
        'vcreated_at',
        {
          ...attributes,
          defaultValue: '2024-02-04T11:00:00Z',
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'location_versions',
        'vupdated_at',
        {
          ...attributes,
          defaultValue: '2024-02-04T11:08:00Z',
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
