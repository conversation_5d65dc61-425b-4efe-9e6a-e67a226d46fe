#!/bin/bash

# npm run migrate-rm-meta-seed

source .env
echo "Dropping tables reservation_options, customer_users, business_users, manually_created_customers, reservations, calendars, gs_for_reservations, and balance_rows"
psql postgres://$COCKROACH_username:$COC<PERSON>RO<PERSON><PERSON>_password@$COCKROACH_host:$COCKROACH_port/defaultdb << EOF
  drop table if exists reservation_options;
  drop table if exists customer_users;
  drop table if exists business_users;
  drop table if exists manually_created_customers;
  drop table if exists reservations;
  drop table if exists calendars;
  drop table if exists gs_for_reservations;
  drop table if exists balance_rows;
EOF