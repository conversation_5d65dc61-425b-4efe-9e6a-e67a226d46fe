'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const version_attributes = {
        type: DataTypes.INTEGER,
        allowNull: false,
      };

      await queryInterface.changeColumn(
        'reservation_options',
        'every',
        version_attributes,
        { transaction },
      );
      await queryInterface.changeColumn(
        'reservation_options',
        'srvDuration',
        version_attributes,
        { transaction },
      );

      await queryInterface.removeColumn('reservation_options', 'srvEvery', {
        transaction,
      });

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const version_attributes = {
        type: DataTypes.INTEGER,
        allowNull: true,
      };

      await queryInterface.changeColumn(
        'reservation_options',
        'every',
        version_attributes,
        { transaction },
      );
      await queryInterface.changeColumn(
        'reservation_options',
        'srvDuration',
        version_attributes,
        { transaction },
      );

      await queryInterface.addColumn(
        'reservation_options',
        'srvEvery',
        {
          type: DataTypes.INTEGER,
          allowNull: true, // set to false in next migration
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
