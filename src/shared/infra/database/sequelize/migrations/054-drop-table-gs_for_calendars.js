'use strict';

module.exports = {
  async up(queryInterface,) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.sequelize.query(
        `
          drop table if exists gs_for_calendars;
      `,
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      console.log(`No down migration done for this migration.`);

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};