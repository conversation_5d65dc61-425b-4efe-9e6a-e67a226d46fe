'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM reservations;
      `,
        { transaction },
      );
      for (const record of records[0]) {
        const { id, reservationOptionId } = record;

        await queryInterface.sequelize.query(
          `
          UPDATE reservations SET "roId" = '${reservationOptionId}' WHERE id = '${id}';
        `,
          { transaction },
        );
      }

      await queryInterface.changeColumn(
        'reservations',
        'roId',
        {
          type: DataTypes.UUID,
          allowNull: false,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM reservations;
      `,
        { transaction },
      );
      for (const record of records[0]) {
        const { id, roId } = record;

        await queryInterface.sequelize.query(
          `
          UPDATE reservations SET "reservationOptionId" = '${roId}' WHERE id = '${id}';
        `,
          { transaction },
        );
      }

      await queryInterface.changeColumn(
        'reservations',
        'reservationOptionId',
        {
          type: DataTypes.UUID,
          allowNull: false,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};