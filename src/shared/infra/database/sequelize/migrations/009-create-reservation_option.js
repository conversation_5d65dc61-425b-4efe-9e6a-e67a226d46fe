'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    await queryInterface.createTable(
      'reservation_options',

      {
        id: {
          type: DataTypes.UUID,
          allowNull: false,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        name: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        active: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
        },

        srvName: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        srvDescription: {
          type: DataTypes.TEXT,
        },
        srvEvery: {
          type: DataTypes.INTEGER,
          allowNull: false,
        },
        srvPrice: {
          type: DataTypes.FLOAT,
        },

        locName: {
          type: DataTypes.TEXT,
        },
        locDescription: {
          type: DataTypes.TEXT,
        },
        locIdentifier: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        locShowInfoWhenReserving: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
        },

        trHour: {
          type: DataTypes.INTEGER,
          allowNull: false,
        },
        trMinute: {
          type: DataTypes.INTEGER,
          allowNull: false,
        },
        trZone: {
          type: DataTypes.TEXT,
          allowNull: false,
        },

        noteWhile: {
          type: DataTypes.TEXT,
        },
        noteAfter: {
          type: DataTypes.TEXT,
        },

        created_at: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updated_at: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        deleted_at: {
          type: DataTypes.DATE,
        },
      },
    );
  },
  async down(queryInterface) {
    await queryInterface.dropTable('reservation_options');
  },
};
