'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'reservations',
        'srvName',
        {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        { transaction }
      );
      await queryInterface.addColumn(
        'reservations',
        'srvDescription',
        {
          type: DataTypes.TEXT,
        },
        { transaction }
      );
      await queryInterface.addColumn(
        'reservations',
        'srvDuration',
        {
          type: DataTypes.INTEGER,
          allowNull: false,
        },
        { transaction }
      );
      await queryInterface.addColumn(
        'reservations',
        'srvPrice',
        {
          type: DataTypes.FLOAT,
        },
        { transaction }
      );

      await queryInterface.addColumn(
        'reservations',
        'locName',
        {
          type: DataTypes.TEXT,
        },
        { transaction }
      );
      await queryInterface.addColumn(
        'reservations',
        'locId',
        {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        { transaction }
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(
        'reservations',
        'srvName',
        { transaction },
      );
      await queryInterface.removeColumn(
        'reservations',
        'srvDescription',
        { transaction },
      );
      await queryInterface.removeColumn(
        'reservations',
        'srvDuration',
        { transaction },
      );
      await queryInterface.removeColumn(
        'reservations',
        'srvPrice',
        { transaction },
      );

      await queryInterface.removeColumn(
        'reservations',
        'locName',
        { transaction },
      );
      await queryInterface.removeColumn(
        'reservations',
        'locId',
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};