'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(
      'balance_rows',

      {
        // Composite key
        userId: {
          type: Sequelize.UUID,
          allowNull: false,
          primaryKey: true,
        },
        index: {
          type: Sequelize.INTEGER,
          allowNull: false,
          primaryKey: true,
        },

        balance: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        movement: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        change: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        reservation: {
          type: Sequelize.JSON,
        },
        date: {
          type: Sequelize.TEXT,
          allowNull: false,
        },

        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      },
    );
  },
  async down(queryInterface) {
    await queryInterface.dropTable('balance_rows');
  },
};
