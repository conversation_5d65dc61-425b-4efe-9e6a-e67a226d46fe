'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    await queryInterface.createTable(
      'reservations',

      {
        id: {
          type: DataTypes.UUID,
          allowNull: false,
          primaryKey: true,
        },
        start: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        customerType: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        cancelledBy: {
          type: DataTypes.TEXT,
        },
        cancelledDate: {
          type: DataTypes.TEXT,
        },
        cancellationReason: {
          type: DataTypes.TEXT,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        business: {
          type: DataTypes.JSON,
          allowNull: false,
        },
        customerId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        customer: {
          type: DataTypes.JSON,
          allowNull: false,
        },
        eventId: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        reservationOptionId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        reservationOption: {
          type: DataTypes.JSON,
          allowNull: false,
        },
        created_at: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updated_at: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        deleted_at: {
          type: DataTypes.DATE,
        },
      },
    );
  },
  async down(queryInterface) {
    await queryInterface.dropTable('reservations');
  },
};
