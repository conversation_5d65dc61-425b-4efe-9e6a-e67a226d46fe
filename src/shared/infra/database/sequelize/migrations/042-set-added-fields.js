'use strict';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM balance_rows;
      `,
        { transaction },
      );
      for (const record of records[0]) {
        const { userId, index, reservation } = record;
        if (!reservation) continue;
        const { id: reservationId, customerFirstName, customerLastName, srvName } = reservation;
        const customerFullName = getFullName({
          firstName: customerFirstName,
          lastName: customerLastName,
        });

        await queryInterface.sequelize.query(
          `
          UPDATE balance_rows SET "reservationId" = '${reservationId}',
                                  "srvName" = '${srvName}',
                                  "customerFullName" = '${customerFullName}'
          WHERE "userId" = '${userId}' AND index = ${index};
        `,
          { transaction },
        );
      }

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM balance_rows;
      `,
        { transaction },
      );
      for (const record of records[0]) {
        const { userId, index, reservationId, customerFullName, srvName } = record;
        if (!reservationId)
          continue;
        if (!customerFullName || !srvName)
          throw Error('customerFirstName or srvName is null');

        const reservationJSON = JSON.stringify({
          id: reservationId,
          customerFirstName: customerFullName,
          srvName,
        })

        await queryInterface.sequelize.query(
          `
          UPDATE balance_rows SET "reservation" = '${reservationJSON}'
          WHERE "userId" = '${userId}' AND index = ${index};
        `,
          { transaction },
        );
      }

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};

function getFullName({
  firstName,
  lastName,
}) {
  if (firstName === null && lastName === null) {
    throw Error('firstName and lastName are both null');
  }

  return `${firstName? firstName : ''}` +
    `${firstName && lastName? ' ' : ''}` +
    `${lastName? lastName : ''}`;
}