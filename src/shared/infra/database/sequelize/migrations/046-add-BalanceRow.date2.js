'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.addColumn(
        'balance_rows',
        'date2',
        {
          type: DataTypes.DATE,
          // allowNull: false, set below
        },
        { transaction },
      );

      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM balance_rows;
      `,
        { transaction },
      );
      for (const record of records[0]) {
        const { userId, index, date } = record;
        const date2value = new Date(date).toJSON();

        await queryInterface.sequelize.query(
          `
          UPDATE balance_rows SET "date2" = '${date2value}'
          WHERE "userId" = '${userId}' AND index = ${index};
        `,
          { transaction },
        );
      }

      await queryInterface.changeColumn(
        'balance_rows',
        'date2',
        {
          type: DataTypes.DATE,
          allowNull: false,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.removeColumn(
        'balance_rows',
        'date2',
        {
          transaction,
        },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};