'use strict';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM reservation_options;
      `,
        { transaction },
      );

      for (const record of records[0]) {
        const { id, srvEvery, version } = record;

        await queryInterface.sequelize.query(
          `
          UPDATE reservation_options SET 
            version = ${version + 1},
            every = ${srvEvery},
            "srvDuration" = ${srvEvery}
          WHERE id = '${id}';
        `,
          { transaction },
        );
      }

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM reservation_options;
      `,
        { transaction },
      );

      for (const record of records[0]) {
        const { id, version, every } = record;

        await queryInterface.sequelize.query(
          `
          UPDATE reservation_options SET 
            version = ${version - 1},
            "srvEvery" = ${every}
          WHERE id='${id}';
        `,
          { transaction },
        );
      }

      await queryInterface.sequelize.query(
        `
        UPDATE reservation_options SET every = null;
        UPDATE reservation_options SET "srvDuration"=null;
      `,
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
