'use strict';

/* eslint-disable @typescript-eslint/no-var-requires */
const { ReservationOptionsInDb } = require('./ReservationOptions.json');
const { CustomerUsersInDb } = require('./CustomerUsers.json');
const { BusinessUsersInDb } = require('./BusinessUsers.json');
const {
  ManuallyCreatedCustomersInDb,
} = require('./ManuallyCreatedCustomers.json');
// } = require('./ManuallyCreatedCustomersMany.json');
const { ReservationsInDb } = require('./Reservations.json');
const { CalendarsInDb } = require('./Calendars.json');
const {
  GeneralSettingsForReservationsInDb,
} = require('./GeneralSettingsForReservations.json');
const {
  BalanceRowsInDb,
} = require('./BalanceRows.json');

const omit = ` migration from 901-seed.js is omitted as it's only run on "dev" stage`;

module.exports = {
  up: async (queryInterface, Sequelize) => {
    if (process.env.STAGE !== 'dev') return console.log('up' + omit);

    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.bulkDelete('reservation_options', null, {
        transaction,
      });
      await queryInterface.bulkInsert(
        'reservation_options',
        ReservationOptionsInDb,
        { transaction },
      );
      await queryInterface.bulkDelete('customer_users', null, { transaction });
      await queryInterface.bulkInsert('customer_users', CustomerUsersInDb, {
        transaction,
      });
      await queryInterface.bulkDelete('business_users', null, { transaction });
      await queryInterface.bulkInsert('business_users', BusinessUsersInDb, {
        transaction,
      });
      await queryInterface.bulkDelete('manually_created_customers', null, {
        transaction,
      });
      await queryInterface.bulkInsert(
        'manually_created_customers',
        ManuallyCreatedCustomersInDb,
        { transaction },
      );
      await queryInterface.bulkDelete('reservations', null, { transaction });
      await queryInterface.bulkInsert(
        'reservations',
        ReservationsInDb,
        { transaction },
      );
      await queryInterface.bulkDelete('calendars', null, { transaction });
      await queryInterface.bulkInsert('calendars', CalendarsInDb, {
        transaction,
      });
      await queryInterface.bulkDelete('gs_for_reservations', null, {
        transaction,
      });
      await queryInterface.bulkInsert(
        'gs_for_reservations',
        GeneralSettingsForReservationsInDb,
        {
          transaction,
        },
      );
      await queryInterface.bulkDelete('balance_rows', null, {
        transaction,
      });
      await queryInterface.bulkInsert(
        'balance_rows',
        BalanceRowsInDb,
        { transaction },
        {
          reservation: { type: new Sequelize.JSON() },
        },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  down: async (queryInterface) => {
    if (process.env.STAGE !== 'dev') return console.log('down' + omit);

    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.bulkDelete('reservation_options', null, {
        transaction,
      });
      await queryInterface.bulkDelete('customer_users', null, { transaction });
      await queryInterface.bulkDelete('business_users', null, { transaction });
      await queryInterface.bulkDelete('manually_created_customers', null, {
        transaction,
      });
      await queryInterface.bulkDelete('reservations', null, { transaction });
      await queryInterface.bulkDelete('calendars', null, { transaction });
      await queryInterface.bulkDelete('gs_for_reservations', null, {
        transaction,
      });
      await queryInterface.bulkDelete('balance_rows', null, {
        transaction,
      });

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
