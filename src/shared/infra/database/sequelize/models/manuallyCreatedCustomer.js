'use strict';
/* eslint-disable @typescript-eslint/no-var-requires */
const { DataTypes } = require('sequelize');

const attributes = {
  id: {
    type: DataTypes.UUID,
    allowNull: false,
    primaryKey: true,
  },
  firstName: {
    type: DataTypes.TEXT,
  },
  lastName: {
    type: DataTypes.TEXT,
  },
  fullName: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  notes: {
    type: DataTypes.TEXT,
  },
  createdBy: {
    type: DataTypes.UUID,
    allowNull: false,
  },

  // These timestamps aren't included in the ManuallyCreatedCustomer domain model, they are handled by <PERSON>quelize. Configured in ../config/config.js
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  deleted_at: {
    type: DataTypes.DATE,
  },
};

module.exports = {
  connect: (connection) => {
    return connection.define('manually_created_customer', attributes, {
      paranoid: true,
      deletedAt: 'deleted_at',
    });
  },
  attributes,
};
