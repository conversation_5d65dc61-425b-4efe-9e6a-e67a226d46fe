'use strict';
/* eslint-disable @typescript-eslint/no-var-requires */
const { DataTypes } = require('sequelize');

const attributes = {
  id: {
    type: DataTypes.UUID,
    allowNull: false,
    primaryKey: true,
  },
  cancellationUpTo: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  maxSimultaneousReservations: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  reminderTemplate: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  title: {
    type: DataTypes.TEXT,
  },
  welcome: {
    type: DataTypes.TEXT,
  },
  slug: {
    type: DataTypes.TEXT,
  },
  phoneRequired: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
  },
  resLimMaxDaysAhead: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  resLimMinTimeBeforeService: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
};

module.exports = {
  connect: (connection) => {
    return connection.define('gs_for_reservation', attributes);
  },
  attributes,
};
