'use strict';
/* eslint-disable @typescript-eslint/no-var-requires */
const { DataTypes } = require('sequelize');

const attributes = {
  id: {
    type: DataTypes.UUID,
    allowNull: false,
    primaryKey: true,
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  name: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
  },
  version: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  every: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },

  srvName: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  srvDescription: {
    type: DataTypes.TEXT,
  },
  srvDuration: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  srvPrice: {
    type: DataTypes.FLOAT,
  },

  locName: {
    type: DataTypes.TEXT,
  },
  locDescription: {
    type: DataTypes.TEXT,
  },
  locIdentifier: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  locShowInfoWhenReserving: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
  },

  trHour: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  trMinute: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  trZone: {
    type: DataTypes.TEXT,
    allowNull: false,
  },

  noteWhile: {
    type: DataTypes.TEXT,
  },
  noteAfter: {
    type: DataTypes.TEXT,
  },

  // These timestamps aren't included in the ReservationOption domain model, they are handled by Sequelize. Configured in ../config/config.js
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  deleted_at: {
    type: DataTypes.DATE,
  },
};

module.exports = {
  connect: (connection) => {
    return connection.define('reservation_option', attributes, {
      paranoid: true,
      deletedAt: 'deleted_at',
    });
  },
  attributes,
};
