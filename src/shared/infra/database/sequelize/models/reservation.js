'use strict';
/* eslint-disable @typescript-eslint/no-var-requires */
const { DataTypes } = require('sequelize');

const { attributes: reservationOptionAttributes } = require('./reservationOption');

const attributes = {
  id: {
    type: DataTypes.UUID,
    allowNull: false,
    primaryKey: true,
  },
  start: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  customerType: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  cancelledBy: {
    type: DataTypes.TEXT,
  },
  cancelledDate: {
    type: DataTypes.TEXT,
  },
  cancellationReason: {
    type: DataTypes.TEXT,
  },
  cancelReservationEventErrors: {
    type: DataTypes.ARRAY(DataTypes.TEXT),  //  only available in Postgres
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  customerId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  customerFirstName: {
    type: DataTypes.TEXT,
  },
  customerLastName: {
    type: DataTypes.TEXT,
  },
  customerPhone: {
    type: DataTypes.TEXT,
  },
  customerEmail: {
    type: DataTypes.TEXT,
  },
  eventId: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  roId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  roZone: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  srvName: reservationOptionAttributes.srvName,
  srvDescription: reservationOptionAttributes.srvDescription,
  srvDuration: reservationOptionAttributes.srvDuration,
  srvPrice: reservationOptionAttributes.srvPrice,
  locName: reservationOptionAttributes.locName,
  // locId: reservationOptionAttributes.locIdentifier,  Da error ya que los campos tienen diferentes nombres, entonces:
  locId: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  reminder: {
    type: DataTypes.TEXT,
    allowNull: false,
    defaultValue: 'NOT_SENT',
  },

  // These timestamps aren't included in the Reservation domain model, they are handled by Sequelize. Configured in ../config/config.js
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  deleted_at: {
    type: DataTypes.DATE,
  },
};

module.exports = {
  connect: (connection) => {
    return connection.define('reservation', attributes, {
      paranoid: true,
      deletedAt: 'deleted_at',
    });
  },
  attributes,
};
