'use strict';
/* eslint-disable @typescript-eslint/no-var-requires */
const { DataTypes } = require('sequelize');

const attributes = {
  id: {
    type: DataTypes.UUID,
    allowNull: false,
    primaryKey: true,
  },
  firstName: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  lastName: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  email: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  phone: {
    type: DataTypes.TEXT,
  },
  timezone: {
    type: DataTypes.TEXT,
    allowNull: false,
  },

  // These timestamps aren't included in the CustomerUser domain model, they are handled by Sequelize. Configured in ../config/config.js
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  deleted_at: {
    type: DataTypes.DATE,
  },
};

module.exports = {
  connect: (connection) => {
    return connection.define('customer_user', attributes, {
      paranoid: true,
      deletedAt: 'deleted_at',
    });
  },
  attributes,
};
