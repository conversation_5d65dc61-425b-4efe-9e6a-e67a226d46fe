import { BaseError } from './AppError';
import { Dat } from '@shared/core/Dat';
import { isUnexpectedError, UnexpectedError } from '@shared/decorators/UnexpectedError';

export class Envelope<T> {
  public readonly result?: T;
  public readonly errorMessage?: string;
  public readonly errorType?: string;
  public readonly time: string;
  public readonly errors?: [BaseError, ...BaseError[]] | [UnexpectedError, ...UnexpectedError[]];

  public constructor(result?: T, errors?: [BaseError, ...BaseError[]] | [UnexpectedError, ...UnexpectedError[]]) {
    if (result !== undefined && errors !== undefined) {
      throw 'Envelope should have either result or errors, not both';
    }

    if (result !== undefined) this.result = result;

    if (errors) {
      this.errors = errors;

      if (errors.length > 1) {
        if (
          errors.some(isUnexpectedError)
        ) throw Error('UnexpectedError should be the unique error when exists');

        this.errorMessage = 'Multiple errors';
        this.errorType = 'MultipleErrors';
      } else {
        this.errorMessage = errors[0].message;
        this.errorType = errors[0].type;
      }
    }

    this.time = Dat.create().value.s;

    Object.freeze(this);
  }

  public static ok<U>(result?: U): Envelope<U> {
    return new Envelope<U>(result);
  }

  public static errors(errors: [BaseError, ...BaseError[]] | [UnexpectedError, ...UnexpectedError[]]): Envelope<undefined> {
    return new Envelope<undefined>(undefined, errors);
  }
}
