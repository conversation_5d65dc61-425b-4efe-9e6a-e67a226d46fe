import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { OptionalLng, getLng, PossibleLngs } from '@shared/utils/utils';

export namespace N0Errors {
  export class CantBeLessThan0 extends BadRequest {
    public constructor(args: { value: number } & OptionalLng) {
      const { value: lessThan0, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: `${t.cantBeLessThan0}: ${lessThan0}`,
      });
    }
  }
  export class MaxValueExceeded extends BadRequest {
    public constructor(lng: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({
        message: `${t.maxValueExceeded}`,
      });
    }
  }
}

const trans = {
  en: {
    cantBeLessThan0: `Can't be less than 0, but this was entered`,
    maxValueExceeded: `Maximum value exceeded`,
  },
  es: {
    cantBeLessThan0: 'No puede ser menor que 0, pero esto fue ingresado',
    maxValueExceeded: 'Valor máximo excedido',
  },
};

patch({ N0Errors });
