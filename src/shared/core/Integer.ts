import { ValueObject } from '@shared/core/domain/ValueObject';
import { IntegerErrors } from './IntegerErrors';
import { getLng, OptionalLng } from '@shared/utils/utils';
import { Result } from '@shared/core/Result';

interface IntegerProps {
  value: number;
}

type IntegerDto = number;

export class Integer extends ValueObject<IntegerProps, IntegerDto> {
  private __class = this.constructor.name;
  public declare props: IntegerProps;  // make props modifiable to support add()
  get value(): number {
    return this.props.value;
  }

  private constructor(props: IntegerProps) {
    super(props);
  }

  public static create(args: { value: number } & OptionalLng): Result<Integer> {
    const { value, lng: _lng } = args;
    const lng = getLng(_lng);
    
    if (!Number.isInteger(value))
      return Result.fail(new IntegerErrors.ShouldBeInteger({ value, lng }));

    return Result.ok<Integer>(new Integer({ value }));
  }

  public toDto(): IntegerDto {
    return this.value;
  }

  public add(by: Integer) {
    this.props = {
      value: this.props.value + by.value,
    };
  }
}
