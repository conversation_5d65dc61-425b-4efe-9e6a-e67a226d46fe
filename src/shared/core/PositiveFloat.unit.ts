import { it, expect, describe } from 'vitest';
import { PositiveFloat } from './PositiveFloat';
import { expectErrorResult } from '@shared/utils/test';

it(`creates`, () => {
  const result = PositiveFloat.create({ value: 1.5 });

  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      value: 1.5,
    },
  });
});

describe(`creation fails if value`, () => {
  it(`is negative`, () => {
    const result = PositiveFloat.create({ value: -1 });

    expectErrorResult({
      result,
      error: 'PositiveFloatErrors.CantBeLessThan0',
      code: 400,
    });
  });

  it(`is 0`, () => {
    const result = PositiveFloat.create({ value: 0 });

    expectErrorResult({
      result,
      error: 'PositiveFloatErrors.CantBe0',
      code: 400,
    });
  });
  it(`is NaN`, () => {
    const result = PositiveFloat.create({ value: NaN });

    expectErrorResult({
      result,
      error: `PositiveFloatErrors.NaN`,
      code: 400,
    });
  });
});
