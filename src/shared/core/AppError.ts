import { Status, StatusError } from '@shared/core/Status';
import { isPrimitive } from '@shared/utils/utils';

export type BaseErrorDto = {
  type: string;
  message: string;
  status: number;
  field?: string;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isBaseErrorArray(test: any): test is [BaseError, ...BaseError[]] {
  if (!test || !test.length) return false;
  for (const error of test) {
    if (!(error instanceof BaseError)) return false;
  }
  return true;
}

export class BaseError {
  public type: string;
  public field?: string;
  public readonly message: string;
  public readonly status: StatusError; // Sometimes the error is found down inside functions and to bubble the correct error type (i.e. BAD_REQUEST 400, NOT_FOUND 404) up to the controller, we need to save it in the error.

  public toDto(): BaseErrorDto {
    return {
      type: this.type,
      message: this.message,
      status: this.status,
      ...this.field !== undefined && { field: this.field },
    };
  }

  protected constructor(args: {
    message: string;
    status: StatusError;
  }) {
    const { message, status } = args;
    this.message = message;
    this.status = status;
    this.type = this.constructor.name;
  }

  private setType(type: string) {
    this.type = type;
  }

  public static assemble(dto: BaseErrorDto) {
    const error = new BaseError(dto);
    error.setType(dto.type);
    if (dto.field) error.setField(dto.field)
    return error;
  }

  public setField(field: string) {
    if (this.field) throw Error(`Field already set`);
    this.field = field;
    return this;
  }

  public prefixField(prefix: string) {
    this.field = prefix + '.' + this.field;
    return this;
  }

  public clone(field: string): BaseError {
    for (const key in this) {
      if (!isPrimitive(this[key]))
        throw Error(`Cannot clone BaseError with non-primitive properties`);
    }
    return BaseError.assemble({
      ...this.toDto(),
      field,
    });
  }
}

export abstract class BadRequest extends BaseError {
  protected constructor(args: { message: string }) {
    const { message } = args;
    super({ message, status: Status.BAD_REQUEST });
  }
}

export abstract class NotFound extends BaseError {
  protected constructor(args: { message: string }) {
    const { message } = args;
    super({ message, status: Status.NOT_FOUND });
  }
}

// formatErrors -> AppSyncController -> Envelope.error(error)/Envelope.errors(errors) -> adaptResult (configurado al llamar addResolverWithLambda en MyStack.ts) siempre envía un array de errores "errors" para estandarizar su manejo en el frontend
export const formatErrors = (errors: BaseError[]) => {
  if (!errors.length) throw Error(`Empty errors array`);
  const status = errors.length === 1 ? errors[0].status : StatusError.BAD_REQUEST;
  return { status, result: errors as [BaseError, ...BaseError[]] };
};