export enum StatusSuccess {
  OK = 200,
  CREATED = 201,
}
export const possibleSuccesses = Object.values(StatusSuccess).filter(
  (value) => typeof value === 'number',
) as number[];

export enum StatusError {
  CONFLICT = 409,
  BAD_REQUEST = 400,
  NOT_FOUND = 404,
  INTERNAL_ERROR = 500,
  BAD_GATEWAY = 502,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  GONE = 410,
  TOO_MANY_REQUESTS = 429,
}

export const Status = {
  ...StatusSuccess,
  ...StatusError,
};
