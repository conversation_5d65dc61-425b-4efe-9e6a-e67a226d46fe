import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { getLng, OptionalLng } from '@shared/utils/utils';

type Value = number | string | Date;
export namespace DatErrors {
  export class InvalidValue extends BadRequest {
    public constructor(args: { value: Value } & OptionalLng) {
      const { value, lng } = args;
      const t: Record<Key, (value: Value) => string> = trans[getLng(lng)];
      super({ message: t.invalidValue(value) });
    }
  }
}

type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    invalidValue(value: Value) {
      return `Date can't be created from ${value}`;
    },
  },
  es: {
    invalidValue(value: Value) {
      return `No se puede crear una fecha a partir de ${value}`;
    },
  },
};

patch({ DatErrors });
