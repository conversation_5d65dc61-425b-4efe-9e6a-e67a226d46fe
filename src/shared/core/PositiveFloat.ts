import { ValueObject } from '@shared/core/domain/ValueObject';
import { PositiveFloatErrors } from './PositiveFloatErrors';
import { Result } from '@shared/core/Result';
import { getLng, OptionalLng } from '@shared/utils/utils';

interface PositiveFloatProps {
  value: number;
}

type PositiveFloatDto = number;

export class PositiveFloat extends ValueObject<
  PositiveFloatProps,
  PositiveFloatDto
> {
  private __class = this.constructor.name;
  get value(): number {
    return this.props.value;
  }

  private constructor(props: PositiveFloatProps) {
    super(props);
  }

  public static create(args: { value: number } & OptionalLng): Result<PositiveFloat> {
    const { value, lng: _lng } = args;
    const lng = getLng(_lng);

    if (isNaN(value)) {
      const error = new PositiveFloatErrors.NaN(lng);
      return Result.fail(error);
    }


    if (value < 0)
      return Result.fail(new PositiveFloatErrors.CantBeLessThan0({ value, lng }));

    if (value === 0) return Result.fail(new PositiveFloatErrors.CantBe0(lng));

    return Result.ok<PositiveFloat>(new PositiveFloat({ value }));
  }

  public toDto(): PositiveFloatDto {
    return this.value;
  }
}
