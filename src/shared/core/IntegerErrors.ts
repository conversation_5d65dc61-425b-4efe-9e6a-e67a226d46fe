import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { OptionalLng, getLng } from '@shared/utils/utils';

export namespace IntegerErrors {
  export class ShouldBeInteger extends BadRequest {
    public constructor(args: { value: number } & OptionalLng) {
      const { value: notAnInteger, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: `${t.shouldBeInteger}: ${notAnInteger}`,
      });
    }
  }
}

type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    shouldBeInteger: 'Should be an integer, but this was entered',
  },
  es: {
    shouldBeInteger: 'Debe ser un entero, pero esto fue ingresado',
  },
};

patch({ IntegerErrors });
