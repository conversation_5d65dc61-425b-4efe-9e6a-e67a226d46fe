import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';

export namespace TimeZoneErrors {
  export class InvalidTimeZone extends BadRequest {
    public constructor(zone: string) {
      if (zone === '') {
        super({ message: 'Time zone cannot be empty' });
      } else {
        super({ message: `Invalid time zone: "${zone}"` });
      }
    }
  }
}

patch({ TimeZoneErrors });
