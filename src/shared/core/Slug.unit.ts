import { expect, test, it } from 'vitest';
import { Slug } from './Slug';
import {
  expectErrorsResult,
  expectMultipleErrorsResult,
} from '@shared/utils/test';
import { SlugErrors } from '@shared/core/SlugErrors';
import { BaseError } from '@shared/core/AppError';

test.each(['0', 'a', 'vwefw-__3243-we43'])(`creates from %s`, (value: string) => {
  const result = Slug.create({slug: value});

  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      value,
    },
  });
});

it(`fails for empty`, () => {
  const result = Slug.create({slug: ''});

  expectErrorsResult({
    result,
    error: `SlugErrors.EmptyNotAllowed`,
    code: 400,
  });
});

test.each([
  ['_', [
    new SlugErrors.ShouldStartWithAletterOrNumber(),
    new SlugErrors.ShouldEndWithAletterOrNumber(),
  ]],
  ['-', [
    new SlugErrors.ShouldStartWithAletterOrNumber(),
    new SlugErrors.ShouldEndWithAletterOrNumber(),
  ]],
  ['_w', [
    new SlugErrors.ShouldStartWithAletterOrNumber(),
  ]],
  ['w_', [
    new SlugErrors.ShouldEndWithAletterOrNumber(),
  ]],
  ['-w', [
    new SlugErrors.ShouldStartWithAletterOrNumber(),
  ]],
  ['w-', [
    new SlugErrors.ShouldEndWithAletterOrNumber(),
  ]],
  ['W', [
    new SlugErrors.InvalidChars({ invalidChars: ['W'] }),
    new SlugErrors.ShouldStartWithAletterOrNumber(),
    new SlugErrors.ShouldEndWithAletterOrNumber(),
  ]],
  ['w%w', [
    new SlugErrors.InvalidChars({ invalidChars: ['%'] }),
  ]],
  ['w+w', [
    new SlugErrors.InvalidChars({ invalidChars: ['+'] }),
  ]],
])(
  `fails for %s`,
  (value: string, errors: BaseError[]) => {
    const result = Slug.create({slug: value});

    expectMultipleErrorsResult({
      result,
      errors,
    });
  },
);
