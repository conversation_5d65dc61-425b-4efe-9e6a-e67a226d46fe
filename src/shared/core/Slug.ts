import { ValueObject } from '@shared/core/domain/ValueObject';
import { SlugErrors } from './SlugErrors';
import { OptionalLng } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { BaseError } from '@shared/core/AppError';

interface SlugProps {
  value: string;
}

type SlugDto = string;

export class Slug extends ValueObject<SlugProps, SlugDto> {
  private __class = this.constructor.name;
  get value(): string {
    return this.props.value;
  }

  private constructor(props: SlugProps) {
    super(props);
  }

  public static create(args: { slug: string } & OptionalLng): Result2<Slug> {
    const { slug, lng } = args;

    const errors: BaseError[] = [];

    if (slug === '') errors.push(new SlugErrors.EmptyNotAllowed(lng));

    const invalidChars = slug.match(new RegExp(`[^a-z0-9-_]`, 'g'));
    if (invalidChars) errors.push(new SlugErrors.InvalidChars({
      invalidChars,
      lng,
    }));

    const invalidStart = new RegExp(`^[^a-z0-9]`);
    if (invalidStart.test(slug)) {
      errors.push(new SlugErrors.ShouldStartWithAletterOrNumber(lng));
    }

    const invalidEnd = new RegExp(`[^a-z0-9]$`);
    if (invalidEnd.test(slug)) {
      errors.push(new SlugErrors.ShouldEndWithAletterOrNumber(lng));
    }

    if (errors.length > 0) return Result2.fail(errors);

    return Result2.ok(new Slug({ value: slug }));
  }

  public toDto(): SlugDto {
    return this.value;
  }
}
