import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { PossibleLngs, getLng } from '@shared/utils/utils';

export namespace PositiveIntErrors {
  export class CantBe0 extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.cantBe0 });
    }
  }
}

type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    cantBe0: "Can't be 0.",
  },
  es: {
    cantBe0: 'No puede ser 0.',
  },
};

patch({ PositiveIntErrors });
