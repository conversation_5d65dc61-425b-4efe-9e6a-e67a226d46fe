import { DateTime } from 'luxon';
import { OptionalLng } from '@shared/utils/utils';
import { Result } from '@shared/core/Result';
import { Dat, DatProps } from '@shared/core/Dat';

// As Dat but dto and property "s" has seconds
export class DatSec extends Dat {

  private constructor(props: DatProps) {
    super(props);
  }

  public static create(args?: { value: string | number | Date | DateTime } & OptionalLng): Result<DatSec> {

    const datOrError = Dat.create(args);
    if (datOrError.isFailure) return Result.fail(datOrError.error!);

    const { j, l, t } = datOrError.value;

    return Result.ok(
      new DatSec({
        j,
        l,
        s: l.toUTC().set({ millisecond: 0 }).toISO({ suppressMilliseconds: true })!,
        t,
      }),
    );
  }
}
