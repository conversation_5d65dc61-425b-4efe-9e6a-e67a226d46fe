import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { TimeZoneErrors } from './TimeZoneErrors';
import { IANAZone } from 'luxon';

// Para adquirirla desde el browser o Node.js: Intl.DateTimeFormat().resolvedOptions().timeZone
// Para adquirir las zonas soportadas: Intl.supportedValuesOf("timeZone")
// https://nodatime.org/TimeZones
// https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
interface TimeZoneProps {
  value: string;
}

export type TimeZoneDto = string;

export class TimeZone extends ValueObject<TimeZoneProps, TimeZoneDto> {
  private __class = this.constructor.name;
  get value(): string {
    return this.props.value;
  }

  private constructor(props: TimeZoneProps) {
    super(props);
  }

  public toDto(): TimeZoneDto {
    return this.props.value;
  }

  public static create(value: string): Result<TimeZone> {
    return IANAZone.isValidZone(value)
      ? Result.ok<TimeZone>(new TimeZone({ value }))
      : Result.fail(new TimeZoneErrors.InvalidTimeZone(value));
  }

  public static default(): TimeZone {
    return TimeZone.create('Etc/GMT').value;
  }
}
