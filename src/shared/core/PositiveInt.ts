import { ValueObject } from '@shared/core/domain/ValueObject';
import { PositiveIntErrors } from './PositiveIntErrors';
import { N0 } from '@shared/core/N0';
import { OptionalLng } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';

interface PositiveIntProps {
  value: number;
}

type PositiveIntDto = number;

export class PositiveInt extends ValueObject<PositiveIntProps, PositiveIntDto> {
  private __class = this.constructor.name;
  get value(): number {
    return this.props.value;
  }

  private constructor(props: PositiveIntProps) {
    super(props);
  }

  public static create(args: { value: number } & OptionalLng): Result2<PositiveInt> {
    const { value, lng } = args;
    const n0orErrors = N0.create({ value, lng });
    if (n0orErrors.isFailure) return Result2.fail(n0orErrors.errors!);

    if (value === 0) return Result2.fail([new PositiveIntErrors.CantBe0(lng)]);

    return Result2.ok<PositiveInt>(new PositiveInt({ value }));
  }

  public toDto(): PositiveIntDto {
    return this.value;
  }
}
