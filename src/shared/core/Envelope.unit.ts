import { it, expect, test } from 'vitest';
import { Envelope } from './Envelope';
import { dateFormat, Error1 } from '@shared/utils/test';
import { UnexpectedError } from '@shared/decorators/UnexpectedError';

it('throws when both result and errors are defined', () => {
  expect(() => new Envelope('result', [new Error1()])).toThrow();
});

const error1_1 = new Error1();
const error1_2 = new Error1();

it(`creates a MultipleErrors type when we have several BaseErrors`, () => {
  const envelope = new Envelope(undefined, [
    error1_1,
    error1_2,
  ]);
  expect(envelope).toMatchObject({
    errorType: 'MultipleErrors',
    errorMessage: expect.any(String),
    time: expect.stringMatching(dateFormat),
    errors: [
      {
        type: error1_1.type,
        message: error1_1.message,
        status: error1_1.status,
      },
      {
        type: error1_2.type,
        message: error1_2.message,
        status: error1_2.status,
      },
    ],
  });
});

test(`when we only have one BaseError, it puts its type and message at the Envelope root`, () => {
  const envelope = new Envelope(undefined, [error1_1]);
  expect(envelope).toMatchObject({
    errorType: error1_1.type,
    errorMessage: error1_1.message,
    time: expect.stringMatching(dateFormat),
    errors: [
      {
        type: error1_1.type,
        message: error1_1.message,
        status: error1_1.status,
      },
    ],
  });
});

test(`when we have an UnexpectedError, it puts its type and message at the Envelope root`, () => {
  const unexpected = new UnexpectedError({
    logGroup: 'logGroup',
    requestId: 'requestId',
  })
  const envelope = new Envelope(undefined, [unexpected]);
  expect(envelope).toMatchObject({
    errorType: 'UnexpectedError',
    errorMessage: expect.stringMatching(/unexpected/i),
    time: expect.stringMatching(dateFormat),
    errors: [
      {
        type: unexpected.type,
        message: unexpected.message,
        status: unexpected.status,
        logGroup: unexpected.logGroup,
        requestId: unexpected.requestId,
      },
    ],
  });
});

it(`throws when we have multiple UnexpectedErrors`, () => {
  const unexpected = new UnexpectedError({
    logGroup: 'logGroup',
    requestId: 'requestId',
  });
  expect(() => new Envelope(undefined, [unexpected, unexpected])).toThrow();
});
