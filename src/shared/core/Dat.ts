import { ValueObject } from './domain/ValueObject';
import { DateTime } from 'luxon';
import { getLng, isDate, OptionalLng } from '@shared/utils/utils';
import { Result } from '@shared/core/Result';
import { DatErrors } from '@shared/core/DatErrors';

export type DatProps = {
  j: Date; // Date de JS válido
  l: DateTime; // Luxon
  s: string; // i.e.: 2024-01-25T10:49Z
  t: number;
};

type DatDto = string;

// Creo este ValueObject porque el Date de TypeScript no es muy seguro, por ejemplo:
// d = new Date('invalid string'); d instanceof Date da true
export class Dat extends ValueObject<DatProps, DatDto> {
  private __class = this.constructor.name;
  public declare props: DatProps;
  get j(): Date {
    return this.props.j;
  }
  get l(): DateTime {
    return this.props.l;
  }
  get s(): string {
    return this.props.s;
  }
  get t(): number {
    return this.props.t;
  }

  protected constructor(props: DatProps) {
    super(props);
  }

  public static create(args?: { value: string | number | Date | DateTime } & OptionalLng): Result<Dat> {
    let value, _lng;
    if (args) {
      value = args.value;
      _lng = args.lng;
    } else {
      value = new Date();
    }
    const lng = getLng(_lng);

    let j;
    if (isDateTime(value)) {
      j = value.toJSDate();
    } else {
      j = new Date(value);
      const _isDate = isDate(j);
      if (!_isDate) return Result.fail(new DatErrors.InvalidValue({ value, lng }));
    }

    const l = DateTime.fromJSDate(j);
    return Result.ok(
      new Dat({
        j,
        l,
        s: l.toUTC()
          .set({ millisecond: 0, second: 0 })
          .toISO({ suppressMilliseconds: true, suppressSeconds: true })!,
        t: j.getTime(),
      }),
    );
  }

  public toDto(): DatDto {
    return this.s;
  }

  public stripSeconds() {
    this.props = {
      ...Dat.create({ value: this.j.setSeconds(0, 0) }).value.props,
    };
  }
}

// Verifica que el valor sea un DateTime de Luxon
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isDateTime(test: any): test is DateTime {
  return test && typeof test.toJSDate === 'function' && isDate(test.toJSDate());
}
