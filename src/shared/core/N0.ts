import { ValueObject } from '@shared/core/domain/ValueObject';
import { N0Errors } from './N0Errors';
import { getLng, OptionalLng } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { BaseError } from '@shared/core/AppError';
import { Integer } from '@shared/core/Integer';
import { PositiveInt } from '@shared/core/PositiveInt';
import { MAX_GRAPHQL_INT } from '@shared/infra/appsync/utils';

interface N0Props {
  value: number;
}

type N0Dto = number;

export class N0 extends ValueObject<N0Props, N0Dto> {
  private __class = this.constructor.name;
  public declare props: N0Props;  // make props modifiable to support add()
  get value(): number {
    return this.props.value;
  }

  private constructor(props: N0Props) {
    super(props);
  }

  public static create(args: { value: number } & OptionalLng): Result2<N0> {
    const { value, lng: _lng } = args;
    const lng = getLng(_lng);

    const errors: BaseError[] = [];

    const integerOrError = Integer.create({ value, lng });
    if (integerOrError.isFailure)
      errors.push(integerOrError.error!);

    if (value < 0) errors.push(new N0Errors.CantBeLessThan0({ value, lng }));

    if (value > MAX_GRAPHQL_INT) errors.push(new N0Errors.MaxValueExceeded(lng));

    if (errors.length > 0) return Result2.fail(errors);

    return Result2.ok<N0>(new N0({ value }));
  }

  public toDto(): N0Dto {
    return this.value;
  }

  public add(by: PositiveInt) {
    this.props = {
      value: this.props.value + by.value,
    };
  }
}
