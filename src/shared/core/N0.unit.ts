import { expect, test, it, describe } from 'vitest';
import { N0 } from './N0';
import {
  expectErrorsResult,
  expectErrorsResultContaining,
} from '@shared/utils/test';
import { PositiveInt } from '@shared/core/PositiveInt';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

describe('create.', () => {
  test.each([0, 1])(`from %s`, (value: number) => {
    const result = N0.create({ value });

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        value,
      },
    });
  });

  describe(`fails if value`, () => {
    it(`isn't an integer`, () => {
      const result = N0.create({ value: 1.5 });

      expectErrorsResultContaining({
        result,
        errorContaining: `IntegerErrors.`,
        code: 400,
      });
    });

    it(`is less than 0`, () => {
      const result = N0.create({ value: -1 });

      expectErrorsResult({
        result,
        error: `N0Errors.CantBeLessThan0`,
        code: 400,
      });
    });

    it(`exceeds max value`, () => {
      const result = N0.create({ value: -1 });

      expectErrorsResult({
        result,
        error: `N0Errors.CantBeLessThan0`,
        code: 400,
      });
    });
  });
});

test('add', () => {
  const initialValue = chance.integer({ min: 0, max: 99999999 });
  const n0 = N0.create({ value: initialValue }).value;
  const by = chance.integer({ min: 1, max: 99999999});
  n0.add(PositiveInt.create({ value: by }).value);
  expect(n0.value).toBe(initialValue + by);
});
