import { EntityID } from './EntityID';
import { areObjectsEqual } from '@shared/utils/utils';
import { N0 } from '@shared/core/N0';

export abstract class EntityCK<T, TDto> {
  protected readonly _id: EntityID;
  protected _id2: N0; // used for composite key in BalanceRow @ balance, modifiable to allow incrementing index in BalanceRow.move()
  public readonly props: T;
  protected abstract toDto(): TDto;

  protected constructor(props: T, id: EntityID, id2: N0) {
    this._id = id;
    this._id2 = id2;
    this.props = props;
  }

  public equals(theOther: EntityCK<T, TDto>): boolean {
    if (theOther && theOther.props === undefined) {
      return false;
    }

    if (!isEntity(theOther)) {
      return false;
    }

    if (this.constructor.name !== theOther.constructor.name) return false;

    return areObjectsEqual(this.toDto(), theOther.toDto());
  }

  // Según la conveniencia, se puede implementar assemble() para crear una entidad a partir de un DTO
  // public static assemble(dto: TDto): T
  // En assemble() se puede usar el mét0do create() o el constructor de la entidad.
  // Usar el mét0do create() es más seguro ya que se aplican las validaciones que pueden haber quedado desincronizadas con los valores del repositorio y es bueno que el mét0do create() lo ponga de manifiesto fallando si no se cumplen dichas validaciones.
  // Pero en algunas ocasiones el input que se usa con create() no es igual a las props de la Entity, por lo que en ese caso hace falta usar el constructor de la entidad. Ejemplos de unit tests de este caso:
  // Caso simple: src/modules/reservation/domain/Reservation.unit.ts
  // Caso complejo: src/modules/reservation/domain/ReservationOption.unit.ts
}

const isEntity = (v: unknown): v is EntityCK<unknown, unknown> => {
  return v instanceof EntityCK;
};
