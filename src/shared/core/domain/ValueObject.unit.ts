import { expect, it, describe, test } from 'vitest';
import { ValueObject } from './ValueObject';

interface SampleProps {
  value1: string;
  value2: {
    inner: number;
  };
}

type SampleDto = SampleProps;

export class Sample extends ValueObject<SampleProps, SampleDto> {
  public constructor(props: SampleProps) {
    super(props);
  }

  public toDto(): SampleDto {
    return {
      value1: this.props.value1,
      value2: {
        inner: this.props.value2.inner,
      },
    };
  }
}

export class Sample2 extends Sample {
  public constructor(props: SampleProps) {
    super(props);
  }
}

describe('ValueObject equality', () => {
  it(`should be equal for same class and data`, () => {
    const sample1 = new Sample({ value1: '1', value2: { inner: 1 } });
    const sample2 = new Sample({ value1: '1', value2: { inner: 1 } });
    expect(sample1.equals(sample2)).toBe(true);
  });

  it(`shouldn't be equal for same class and different data`, () => {
    const sample1 = new Sample({ value1: '1', value2: { inner: 1 } });
    const sample2 = new Sample({ value1: '1', value2: { inner: 2 } });
    expect(sample1.equals(sample2)).toBe(false);
  });

  it(`shouldn't be equal for different class and same data`, () => {
    const sample1 = new Sample({ value1: '1', value2: { inner: 1 } });
    const sample2 = new Sample2({ value1: '1', value2: { inner: 1 } });
    expect(sample1.equals(sample2)).toBe(false);
  });
});

test(`toDto()`, () => {
  const input = { value1: '1', value2: { inner: 1 } };
  const sample = new Sample(input);
  expect(sample.toDto()).toMatchObject(input);
});
