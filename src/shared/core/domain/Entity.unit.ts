import { expect, it } from 'vitest';
import { EntityID } from '@shared/core/domain/EntityID';
import { Entity } from '@shared/core/domain/Entity';

interface SampleProps {
  name: string;
  description: string | null;
  price: number;
}

export type SampleDto = {
  id: string;
  name: string;
  description: string | null;
  price: number;
};

class Sample extends Entity<SampleProps, SampleDto> {
  get name(): string {
    return this.props.name;
  }

  get description(): string | null {
    return this.props.description;
  }

  get price(): number {
    return this.props.price;
  }

  private constructor(props: SampleProps, id?: EntityID) {
    super(props, id);
  }

  public static create(props: SampleProps, id?: EntityID): Sample {
    return new Sample(
      {
        ...props,
      },
      id,
    );
  }

  public toDto(): SampleDto {
    const { name, description, price } = this;

    return {
      id: this._id.toString(),
      name,
      description,
      price,
    };
  }

  public static assemble(dto: SampleDto): Sample {
    const { id, name, description, price } = dto;
    return Sample.create(
      {
        name,
        description,
        price,
      },
      new EntityID(id),
    );
  }
}

it(`should be equal if id and data is the same`, () => {
  const sample1 = Sample.assemble({
    name: 'pep',
    description: 'desc',
    price: 10,
    id: 'mismoId',
  });
  const sample2 = Sample.assemble({
    name: 'pep',
    description: 'desc',
    price: 10,
    id: 'mismoId',
  });
  expect(sample1.equals(sample2)).toBe(true);
});

it(`should be equal if id and data is the same 2`, () => {
  const sample1 = Sample.assemble({
    name: 'pep',
    description: null,
    price: 10,
    id: 'mismoId',
  });
  const sample2 = Sample.assemble({
    name: 'pep',
    description: null,
    price: 10,
    id: 'mismoId',
  });
  expect(sample1.equals(sample2)).toBe(true);
});

it(`shouldn't be equal if id is the same but data is not`, () => {
  const sample1 = Sample.assemble({
    name: 'pep',
    description: 'desc',
    price: 10,
    id: 'mismoId',
  });
  const sample2 = Sample.assemble({
    name: 'pepe',
    description: 'desc',
    price: 10,
    id: 'mismoId',
  });
  expect(sample1.equals(sample2)).toBe(false);
});

it(`shouldn't be equal if the data is the same but id is not`, () => {
  const sample1 = Sample.assemble({
    name: 'pep',
    description: 'desc',
    price: 10,
    id: 'id1',
  });
  const sample2 = Sample.assemble({
    name: 'pep',
    description: 'desc',
    price: 10,
    id: 'id2',
  });
  expect(sample1.equals(sample2)).toBe(false);
});
