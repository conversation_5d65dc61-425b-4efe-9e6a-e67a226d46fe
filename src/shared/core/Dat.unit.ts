import { expect, test, it } from 'vitest';
import { Dat } from '@shared/core/Dat';
import { expectErrorResult } from '@shared/utils/test';

test('creation from dto', () => {
  const dto = '2024-12-12T00:00Z';
  const created = Dat.create({ value: dto }).value;
  expect(created.toDto()).toBe(dto);
});

test('without input creates for the current time', () => {
  const before = new Date().getTime();
  const now = Dat.create().value;
  const after = new Date().getTime();
  expect(now.t).toBeGreaterThanOrEqual(before);
  expect(now.t).toBeLessThanOrEqual(after);
});

it('fails for an invalid value', () => {
  const dto = '24-12-12T00:00Z';
  const result = Dat.create({ value: dto });
  expectErrorResult({
    result,
    code: 400,
    error: 'DatErrors.InvalidValue',
  });
});
