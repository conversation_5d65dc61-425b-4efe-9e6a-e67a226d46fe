import { expect, test, describe, it } from 'vitest';
import { BaseError } from '@shared/core/AppError';
import { Error1 } from '@shared/utils/test';

describe('BaseError', () => {
  test(`BaseError.toDto gives an object assemblable to the same error`, () => {
    const error = new Error1();
    const dto = error.toDto();

    const assembled = BaseError.assemble(dto);

    expect(assembled.toDto()).toMatchObject(dto);
  });

  test('BaseError.setField', () => {
    const error = new Error1();
    error.setField('someField');
    const dto = error.toDto();

    expect(dto.field).toBe('someField');

    const assembled = BaseError.assemble(dto);

    expect(assembled.toDto()).toMatchObject(dto);
  });

  it('throws if error field is being overwritten', () => {
    const error = new Error1();
    error.setField('someField');
    expect(() => error.setField('overwrite')).toThrow();
  });

  test('BaseError.prefixField', () => {
    const error = new Error1().setField('someField');
    error.prefixField('prefix');
    const dto = error.toDto();

    expect(dto.field).toBe('prefix.someField');

    const assembled = BaseError.assemble(dto);

    expect(assembled.toDto()).toMatchObject(dto);
  });

  test('BaseError.clone', () => {
    const error = new Error1().setField('someField');
    const cloned = error.clone('anotherField');

    const errorDto = error.toDto();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { field: field1, ...sameProps1  } = errorDto;
    const clonedDto = cloned.toDto();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { field: field2, ...sameProps2  } = clonedDto;

    expect(sameProps1).toMatchObject(sameProps2);

    // Editing cloned error should not affect the original error
    cloned.prefixField('prefix');
    expect(error.toDto().field).toBe('someField');
    expect(cloned.toDto().field).toBe('prefix.anotherField');
  });
});
