import { expect, test, it } from 'vitest';
import { Integer } from './Integer';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

test.each([-1, 0, 1])(`creates from %s`, (value: number) => {
  const result = Integer.create({value});

  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      value,
    },
  });
});

it(`fails if value isn't an integer`, () => {
  const result = Integer.create({value: 1.5});
  expect(result).toMatchObject({
    isFailure: true,
    error: {
      type: 'IntegerErrors.ShouldBeInteger',
      message: expect.any(String),
      status: 400,
    },
  });
});

test('add', () => {
  const initialValue = chance.integer();
  const int = Integer.create({ value: initialValue }).value;
  const byValue = chance.integer();
  const by = Integer.create({ value: byValue }).value;
  int.add(by);
  expect(int.value).toBe(initialValue + byValue);
});