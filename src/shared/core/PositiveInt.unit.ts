import { expect, it, describe } from 'vitest';
import { PositiveInt } from './PositiveInt';
import {
  expectErrorsResult,
  expectErrorsResultContaining,
} from '@shared/utils/test';

it(`creates`, () => {
  const result = PositiveInt.create({ value: 1 });

  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      value: 1,
    },
  });
});

describe(`creation fails if value`, () => {
  it(`isn't a valid N0 number`, () => {
    const result = PositiveInt.create({ value: 1.5 });

    expectErrorsResultContaining({
      result,
      errorContaining: `IntegerErrors.`,
      code: 400,
    });
  });

  it(`is 0`, () => {
    const result = PositiveInt.create({ value: 0 });

    expectErrorsResult({
      result,
      error: `PositiveIntErrors.CantBe0`,
      code: 400,
    });
  });
});
