import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { getLng, OptionalLng, PossibleLngs } from '@shared/utils/utils';

export namespace PositiveFloatErrors {
  export class NaN extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({
        message: t.NaN,
      });
    }
  }
  export class CantBeLessThan0 extends BadRequest {
    public constructor(args: { value: number } & OptionalLng) {
      const { value: lessThan0, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: `${t.cantBeLessThan0}: ${lessThan0}.`,
      });
    }
  }
  export class CantBe0 extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({ message: t.cantBe0 });
    }
  }
}

const trans = {
  en: {
    cantBeLessThan0: `Can't be less than 0, but this was entered`,
    NaN: `Not a valid number.`,
    cantBe0: `Can't be 0.`,
  },
  es: {
    cantBeLessThan0: `No puede ser menor a cero, pero esto fue ingresado`,
    NaN: `No es un número válido.`,
    cantBe0: `No puede ser cero.`,
  },
};

patch({ PositiveFloatErrors });
