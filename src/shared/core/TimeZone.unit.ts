import { expect, test, describe } from 'vitest';
import { TimeZone } from './TimeZone';

test(`creation`, () => {
  const input = 'Pacific/Tongatapu';

  const result = TimeZone.create(input);

  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      value: input,
    },
  });

  const created = result.value;
  const dto = created.toDto();

  expect(dto).toBe(input);
});

describe(`fails for invalid time zone`, () => {
  test.each(['Narnia', 'Europa/New_York', ''])(`%s`, (invalid: string) => {
    const result = TimeZone.create(invalid);

    expect(result).toMatchObject({
      isFailure: true,
      error: {
        status: 400,
        message: expect.any(String),
        type: `TimeZoneErrors.InvalidTimeZone`,
      },
    });
  });
});
