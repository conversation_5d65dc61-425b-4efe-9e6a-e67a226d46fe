import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { OptionalLng, getLng, PossibleLngs } from '@shared/utils/utils';

export namespace SlugErrors {
  export class EmptyNotAllowed extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.emptyEntered });
    }
  }
  export class InvalidChars extends BadRequest {
    public constructor(args: { invalidChars: string[] } & OptionalLng) {
      const {  invalidChars, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: `${t.containsInvalidChars} ${invalidChars.join(', ')}` });
    }
  }
  export class ShouldStartWithAletterOrNumber extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.shouldStartWithAletterOrNumber });
    }
  }
  export class ShouldEndWithAletterOrNumber extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.shouldEndWithAletterOrNumber });
    }
  }
}
type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    containsInvalidChars: 'The value you entered contains invalid chars: ',
    emptyEntered: `The value can't be empty.`,
    shouldStartWithAletterOrNumber: 'The value should start with a lowercase letter or number.',
    shouldEndWithAletterOrNumber: 'The value should end with a lowercase letter or number.',
  },
  es: {
    containsInvalidChars: 'El valor ingresado contiene caracteres inválidos: ',
    emptyEntered: 'El valor no puede estar vacío',
    shouldStartWithAletterOrNumber: 'El valor debe comenzar con una letra minúscula o número.',
    shouldEndWithAletterOrNumber: 'El valor debe terminar con una letra minúscula o número.',
  },
};

patch({ SlugErrors });
