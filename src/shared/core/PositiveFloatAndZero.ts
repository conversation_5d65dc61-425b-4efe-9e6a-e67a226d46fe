import { ValueObject } from '@shared/core/domain/ValueObject';
import { PositiveFloatAndZeroErrors } from './PositiveFloatAndZeroErrors';
import { Result } from '@shared/core/Result';
import { getLng, OptionalLng } from '@shared/utils/utils';

interface PositiveFloatAndZeroProps {
  value: number;
}

type PositiveFloatAndZeroDto = number;

export class PositiveFloatAndZero extends ValueObject<
  PositiveFloatAndZeroProps,
  PositiveFloatAndZeroDto
> {
  private __class = this.constructor.name;
  get value(): number {
    return this.props.value;
  }

  private constructor(props: PositiveFloatAndZeroProps) {
    super(props);
  }

  public static create(args: { value: number } & OptionalLng): Result<PositiveFloatAndZero> {
    const { value, lng: _lng } = args;
    const lng = getLng(_lng);

    if (isNaN(value)) {
      const error = new PositiveFloatAndZeroErrors.NaN(lng);
      return Result.fail(error);
    }


    if (value < 0)
      return Result.fail(new PositiveFloatAndZeroErrors.CantBeLessThan0({ value, lng }));

    return Result.ok<PositiveFloatAndZero>(new PositiveFloatAndZero({ value }));
  }

  public toDto(): PositiveFloatAndZeroDto {
    return this.value;
  }
}
