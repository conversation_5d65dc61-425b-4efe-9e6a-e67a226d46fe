import { it, expect, describe } from 'vitest';
import { PositiveFloatAndZero } from './PositiveFloatAndZero';
import { expectErrorResult } from '@shared/utils/test';

describe(`creation succeeds if value is`, () => {
  it(`float`, () => {
    const result = PositiveFloatAndZero.create({value: 1.5});

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        value: 1.5,
      },
    });
  });
  it(`0`, () => {
    const result = PositiveFloatAndZero.create({value: 0});

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        value: 0,
      },
    });
  });
});

describe(`creation fails if value is`, () => {
  it(`negative`, () => {
    const result = PositiveFloatAndZero.create({ value: -1 });

    expectErrorResult({
      result,
      error: 'PositiveFloatAndZeroErrors.CantBeLessThan0',
      code: 400,
    });
  });

  it(`NaN`, () => {
    const result = PositiveFloatAndZero.create({ value: NaN });

    expectErrorResult({
      result,
      error: `PositiveFloatAndZeroErrors.NaN`,
      code: 400,
    });
  });
});
