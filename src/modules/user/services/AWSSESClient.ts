import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';
import { IEmailClient } from './IEmailClient';
import { Result2 } from '@shared/core/Result2';
import { AWSSESClientErrors } from './AWSSESClientErrors';
import { PossibleLngs } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';

// Validate required environment variables at module load time
const { sesFromEmail } = process.env;
if (!sesFromEmail) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var sesFromEmail!`);
}

/**
 * AWS SES implementation of the IEmailClient interface
 */
export class AWSSESClient implements IEmailClient {
  private readonly client: SESClient;
  private readonly contextProvider: IContextProvider;

  public constructor(args: { contextProvider: IContextProvider }) {
    this.client = new SESClient({});
    this.contextProvider = args.contextProvider;
  }

  /**
   * Sends an email message using AWS SES
   * @param params - The parameters for the email message
   * @returns A Result2 that resolves to undefined on success or errors on failure
   */
  public async send(params: {
    msg: string;
    emailAddress: string;
    subject: string;
    lng: PossibleLngs;
  }): Promise<Result2<undefined>> {
    const { msg, emailAddress, subject, lng } = params;
    try {
      const { MessageId } = await this.client.send(new SendEmailCommand({
        Source: sesFromEmail,
        Destination: {
          ToAddresses: [emailAddress],
        },
        Message: {
          Subject: {
            Data: subject,
            Charset: 'UTF-8',
          },
          Body: {
            Text: {
              Data: msg,
              Charset: 'UTF-8',
            },
          },
        },
      }));
      console.log(`Email sent successfully with ID: ${MessageId}`);
      return Result2.ok(undefined);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      // Log all errors for monitoring
      console.error('AWS SES error:', error);
      const { requestId, cfId, extendedRequestId } = error.$metadata;
      console.log({ requestId, cfId, extendedRequestId });
      
      // Send to analytics for monitoring
      await this.contextProvider.sendAnalytics({ 
        error: error.name || 'Unknown', 
        errorMessage: error.message || 'Unknown error',
        emailAddress,
        metadata: error.$metadata,
      });

      // Handle specific AWS SES errors https://docs.aws.amazon.com/ses/latest/api/API_SendEmail.html#API_SendEmail_Errors
      if (error.name === 'InvalidParameterValue' && 
          error.message.includes('email')) {
        return Result2.fail([new AWSSESClientErrors.InvalidEmailAddress({ lng })]);
      } else if (error.name === 'MessageRejected' || 
                 error.name === 'MailFromDomainNotVerified') {
        return Result2.fail([new AWSSESClientErrors.EmailAddressBounced({ lng })]);
      }
      
      // For all other errors, return a generic internal error
      return Result2.fail([new AWSSESClientErrors.InternalError({ lng })]);
    }
  }
}
