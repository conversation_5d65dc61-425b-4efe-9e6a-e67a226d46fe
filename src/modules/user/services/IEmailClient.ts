import { Result2 } from '@shared/core/Result2';

/**
 * Interface for email client implementations
 */
export interface IEmailClient {
  /**
   * Sends an email message
   * @param params - The parameters for the email message
   * @returns A promise that resolves when the message is sent
   */
  send(params: {
    msg: string;
    emailAddress: string;
    subject: string;
  }): Promise<Result2<undefined>>;
}
