import { test, expect, vi, beforeEach, describe } from 'vitest';
import { EmailSrv } from './EmailSrv';
import { IEmailClient } from './IEmailClient';
import { Result2 } from '@shared/core/Result2';
import { pickLng } from '@shared/utils/test';

const lng = pickLng();

describe('EmailSrv Unit Tests', () => {
  let emailSrv: EmailSrv;
  let mockEmailClient: IEmailClient;

  beforeEach(() => {
    // Create mock email client
    mockEmailClient = {
      send: vi.fn(),
    };
    
    emailSrv = new EmailSrv({ emailClient: mockEmailClient });
  });

  test('service initializes without errors', () => {
    expect(emailSrv).toBeInstanceOf(EmailSrv);
  });

  test('successfully sends verification code in English', async () => {
    // Mock successful email client response
    vi.mocked(mockEmailClient.send).mockResolvedValueOnce(Result2.ok(undefined));

    const params = {
      emailAddress: '<EMAIL>',
      code: 1234,
      lng: 'en' as const,
    };

    const result = await emailSrv.sendCode(params);

    // Verify result
    expect(result.isSuccess).toBe(true);

    // Verify email client was called with correct parameters
    expect(mockEmailClient.send).toHaveBeenCalledTimes(1);
    expect(mockEmailClient.send).toHaveBeenCalledWith({
      msg: 'Your verification code is: 1234',
      emailAddress: '<EMAIL>',
      subject: 'Verification Code',
    });
  });

  test('successfully sends verification code in Spanish', async () => {
    // Mock successful email client response
    vi.mocked(mockEmailClient.send).mockResolvedValueOnce(Result2.ok(undefined));

    const params = {
      emailAddress: '<EMAIL>',
      code: 5678,
      lng: 'es' as const,
    };

    const result = await emailSrv.sendCode(params);

    // Verify result
    expect(result.isSuccess).toBe(true);

    // Verify email client was called with correct Spanish parameters
    expect(mockEmailClient.send).toHaveBeenCalledTimes(1);
    expect(mockEmailClient.send).toHaveBeenCalledWith({
      msg: 'Tu código de verificación es: 5678',
      emailAddress: '<EMAIL>',
      subject: 'Código de verificación',
    });
  });

  test('handles email client failure', async () => {
    // Mock email client failure
    const mockError = new Error('Email sending failed');
    vi.mocked(mockEmailClient.send).mockResolvedValueOnce(Result2.fail([mockError as any]));

    const params = {
      emailAddress: '<EMAIL>',
      code: 1234,
      lng,
    };

    const result = await emailSrv.sendCode(params);

    // Verify result
    expect(result.isFailure).toBe(true);
    expect(result.errors).toEqual([mockError]);

    // Verify email client was called
    expect(mockEmailClient.send).toHaveBeenCalledTimes(1);
  });

  test('defaults to English for unknown language', async () => {
    // Mock successful email client response
    vi.mocked(mockEmailClient.send).mockResolvedValueOnce(Result2.ok(undefined));

    const params = {
      emailAddress: '<EMAIL>',
      code: 1234,
      lng: 'fr' as any, // Unsupported language
    };

    const result = await emailSrv.sendCode(params);

    // Verify result
    expect(result.isSuccess).toBe(true);

    // Verify it defaults to English
    expect(mockEmailClient.send).toHaveBeenCalledWith({
      msg: 'Your verification code is: 1234',
      emailAddress: '<EMAIL>',
      subject: 'Verification Code',
    });
  });
});
