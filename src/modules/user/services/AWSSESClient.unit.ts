import { test, expect, vi, beforeEach, describe } from 'vitest';
import { AWSSESClient } from './AWSSESClient';
import { AWSSESClientErrors } from './AWSSESClientErrors';
import { PossibleLngs } from '@shared/utils/utils';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { pickLng } from '@shared/utils/test';

// Mock AWS SDK
vi.mock('@aws-sdk/client-ses', () => {
  return {
    SESClient: vi.fn().mockImplementation(() => ({
      send: vi.fn(),
    })),
    SendEmailCommand: vi.fn().mockImplementation((params) => ({
      params,
    })),
  };
});

// Import mocked modules
import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';

const lng = pickLng();

describe('AWSSESClient Unit Tests', () => {
  let awsSESClient: AWSSESClient;
  let contextProvider: ContextProvider;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockSESSend: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock environment variable
    vi.stubEnv('sesFromEmail', '<EMAIL>');
    
    contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
    awsSESClient = new AWSSESClient({ contextProvider });
    vi.spyOn(contextProvider, 'sendAnalytics').mockResolvedValue();

    // Get the mocked SES client instance and its send method
    const MockedSESClient = vi.mocked(SESClient);
    // @ts-expect-error opt-out type checking for this mock
    const mockInstance = MockedSESClient.mock.results[MockedSESClient.mock.results.length - 1]?.value;
    mockSESSend = mockInstance?.send;
  });

  test('client initializes without errors', () => {
    expect(awsSESClient).toBeInstanceOf(AWSSESClient);
  });

  test('successfully sends an email message', async () => {
    // Mock successful response
    mockSESSend.mockResolvedValueOnce({ MessageId: 'test-message-id' });

    const params = {
      msg: 'Your verification code is: 1234',
      emailAddress: '<EMAIL>',
      subject: 'Verification Code',
    };

    const result = await awsSESClient.send(params);

    // Verify result
    expect(result.isSuccess).toBe(true);

    // Verify AWS SES was called correctly
    expect(mockSESSend).toHaveBeenCalledTimes(1);
    expect(SendEmailCommand).toHaveBeenCalledWith({
      Source: '<EMAIL>',
      Destination: {
        ToAddresses: [params.emailAddress],
      },
      Message: {
        Subject: {
          Data: params.subject,
          Charset: 'UTF-8',
        },
        Body: {
          Text: {
            Data: params.msg,
            Charset: 'UTF-8',
          },
        },
      },
    });
  });

  test('handles invalid email address error', async () => {
    // Mock AWS SES error for invalid email
    const awsError = {
      name: 'InvalidParameterValue',
      message: 'Invalid email address format',
      $metadata: { requestId: 'test-request-id' },
    };
    mockSESSend.mockRejectedValueOnce(awsError);

    const params = {
      msg: 'Test message',
      emailAddress: 'invalid-email',
      subject: 'Test Subject',
    };

    const result = await awsSESClient.send(params);

    // Verify result
    expect(result.isFailure).toBe(true);
    expect(result.errors?.[0]).toBeInstanceOf(AWSSESClientErrors.InvalidEmailAddress);

    // Verify analytics was called
    expect(contextProvider.sendAnalytics).toHaveBeenCalledWith({
      error: 'InvalidParameterValue',
      errorMessage: 'Invalid email address format',
      emailAddress: params.emailAddress,
      metadata: awsError.$metadata,
    });
  });

  test('handles message rejected error', async () => {
    // Mock AWS SES error for message rejected
    const awsError = {
      name: 'MessageRejected',
      message: 'Email address bounced',
      $metadata: { requestId: 'test-request-id' },
    };
    mockSESSend.mockRejectedValueOnce(awsError);

    const params = {
      msg: 'Test message',
      emailAddress: '<EMAIL>',
      subject: 'Test Subject',
    };

    const result = await awsSESClient.send(params);

    // Verify result
    expect(result.isFailure).toBe(true);
    expect(result.errors?.[0]).toBeInstanceOf(AWSSESClientErrors.EmailAddressBounced);
  });

  test('handles generic AWS error', async () => {
    // Mock generic AWS error
    const awsError = {
      name: 'ServiceUnavailable',
      message: 'Service temporarily unavailable',
      $metadata: { requestId: 'test-request-id' },
    };
    mockSESSend.mockRejectedValueOnce(awsError);

    const params = {
      msg: 'Test message',
      emailAddress: '<EMAIL>',
      subject: 'Test Subject',
    };

    const result = await awsSESClient.send(params);

    // Verify result
    expect(result.isFailure).toBe(true);
    expect(result.errors?.[0]).toBeInstanceOf(AWSSESClientErrors.InternalError);
  });

  test('throws error when sesFromEmail environment variable is not set', () => {
    vi.unstubAllEnvs();
    
    expect(() => {
      new AWSSESClient({ contextProvider });
    }).toThrow('Undefined env var sesFromEmail!');
  });
});
