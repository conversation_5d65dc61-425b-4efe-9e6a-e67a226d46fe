import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { getLng, PossibleLngs } from '@shared/utils/utils';
import { Dat } from '@shared/core/Dat';
import { getRelativeTime } from '../utils/utils';
import { TimeZone } from '@shared/core/TimeZone';

export namespace BusinessUserErrors {
  export class NoAttemptsLeft extends BadRequest {
    public constructor(args: {
      blockedUntil: Dat,
      lng: PossibleLngs,
      timezone: TimeZone | null,
    }) {
      const { blockedUntil, lng, timezone } = args;
      const t = trans[getLng(lng)];
      let relativeTime;
      if (timezone)
        relativeTime = getRelativeTime({ date: blockedUntil, lng, timezone });
      else
        relativeTime = blockedUntil.s + ' (GMT)';
      super({
        message: t.noAttemptsLeft(relativeTime),
      });
    }
  }

  export class NoVerificationInProgress extends BadRequest {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.noVerificationInProgress,
      });
    }
  }

  export class CodeExpired extends BadRequest {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.codeExpired,
      });
    }
  }

  export class InvalidCode extends BadRequest {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.invalidCode,
      });
    }
  }
}

const trans = {
  en: {
    noAttemptsLeft(relativeTime: string) {
      return `Verification code sending limit reached. You can send a new code after ${relativeTime}.`;
    },
    noVerificationInProgress: 'No phone verification in progress. Please request a verification code first.',
    codeExpired: 'Verification code has expired. Please request a new code.',
    invalidCode: 'Invalid verification code. Please request a new code.',
  },
  es: {
    noAttemptsLeft(relativeTime: string) {
      return `Envíos de códigos de verificación agotados. Puedes enviar un nuevo código después de ${relativeTime}.`;
    },
    noVerificationInProgress: 'No hay verificación de teléfono en curso. Por favor solicita un código de verificación primero.',
    codeExpired: 'El código de verificación ha expirado. Por favor solicita un nuevo código.',
    invalidCode: 'Código de verificación inválido. Por favor solicita un nuevo código.',
  },
};

patch({ BusinessUserErrors });
