import { AggregateRoot } from '@shared/core/domain/AggregateRoot';
import { EntityID } from '@shared/core/domain/EntityID';
import { Email } from './Email';
import { Name } from './Name';
import { BusinessUserCreatedEvent } from './events/BusinessUserCreatedEvent';
import { TimeZone } from '@shared/core/TimeZone';
import { getLng, PossibleLngs, Spread } from '@shared/utils/utils';
import { Phone } from './Phone';
import { Dat } from '@shared/core/Dat';
import { Result2 } from '@shared/core/Result2';
import { BusinessUserErrors } from './BusinessUserErrors';
import { Code } from './Code';
import { DatSec } from '@shared/core/DatSec';
import { UserLng } from './UserLng';

type BusinessUserCreateInput = {
  email: Email;
  timezone: TimeZone; // default timezone during registration, frontend sends whatever the browser timezone is.
  // Instead of using PossibleLngs, I use UserLng to follow the standard input validation flow in UpdateBusinessUser.ts use case. This way we can provide a proper error message if userLng is invalid. The lng is used in many places, in which I use PossibleLngs and getLng() as it's simpler, without the need to going through all the formality of a value object.
  lng: UserLng;
};

type BusinessUserUpdateInput = Omit<BusinessUserCreateInput, 'email'> & {
  firstName: Name | null;
  lastName: Name | null;
};

export enum VerificationStatus {
  INITIAL = 'INITIAL',
  STARTED = 'STARTED',
  SUCCESS = 'SUCCESS',
  CODE_EXPIRED = 'CODE_EXPIRED',
  INVALID_CODE = 'INVALID_CODE',
}

export type PhoneVerification = {
  status: VerificationStatus;  // To avoid showing code expiration when the verification has already ended.
  attempts: [Dat, ...Dat[]] | null;
  blockedUntil: Dat | null;
  newPhone: Phone | null;
  code: Code | null;
  codeExpiresAt: DatSec | null;
};
export const getPhoneVerificationNull = () => ({
  status: VerificationStatus.INITIAL,
  attempts: null,
  blockedUntil: null,
  newPhone: null,
  code: null,
  codeExpiresAt: null,
});

export type EmailVerification = {
  status: VerificationStatus;  // To avoid showing code expiration when the verification has already ended.
  attempts: [Dat, ...Dat[]] | null;
  blockedUntil: Dat | null;
  newEmail: Email | null;
  code: Code | null;
  codeExpiresAt: DatSec | null;
};
export const getEmailVerificationNull = () => ({
  status: VerificationStatus.INITIAL,
  attempts: null,
  blockedUntil: null,
  newEmail: null,
  code: null,
  codeExpiresAt: null,
});

export type BusinessUserProps = BusinessUserCreateInput & {
  // Only email is asked for account creation, so at first we don't have their phone, first and last name.
  phone: Phone | null;
  phoneVerification: PhoneVerification;
  emailVerification: EmailVerification;
  firstName: Name | null;
  lastName: Name | null;
};

export type PhoneVerificationDto = Spread<PhoneVerification, {
  status: string;
  attempts: string[] | null;
  blockedUntil: string | null;
  newPhone: string | null;
  code: number | null;
  codeExpiresAt: string | null;
}>;

export type EmailVerificationDto = Spread<EmailVerification, {
  status: string;
  attempts: string[] | null;
  blockedUntil: string | null;
  newEmail: string | null;
  code: number | null;
  codeExpiresAt: string | null;
}>;

export type BusinessUserDto = Spread<
  BusinessUserProps,
  {
    id: string;
    firstName: string | null;
    lastName: string | null;
    lng: string;
    email: string;
    timezone: string;
    phone: string | null;
    phoneVerification: PhoneVerificationDto;
    emailVerification: EmailVerificationDto;
  }
>;

type PhoneVerificationForFrontend = {
  status: string;
  blockedUntil: string | null;
  codeExpiresAt: string | null;
  newPhone: string | null;
};
type EmailVerificationForFrontend = {
  status: string;
  blockedUntil: string | null;
  codeExpiresAt: string | null;
  newEmail: string | null;
};
export type BusinessUserForFrontend = Spread<BusinessUserDto, {
  phoneVerification: PhoneVerificationForFrontend;
  emailVerification: EmailVerificationForFrontend;
}>;

export class BusinessUser extends AggregateRoot<
  BusinessUserProps,
  BusinessUserDto
> {
  private __class = this.constructor.name;
  public static MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS = 3;

  get firstName(): Name | null {
    return this.props.firstName;
  }

  get lastName(): Name | null {
    return this.props.lastName;
  }

  get lng(): UserLng {
    return this.props.lng;
  }

  get email(): Email {
    return this.props.email;
  }

  get timezone(): TimeZone {
    return this.props.timezone;
  }

  get phone(): Phone | null {
    return this.props.phone;
  }

  get phoneVerification(): PhoneVerification{
    return this.props.phoneVerification;
  }

  get emailVerification(): EmailVerification{
    return this.props.emailVerification;
  }

  private constructor(props: BusinessUserProps, id?: EntityID) {
    super(props, id);
  }

  public static create(props: BusinessUserCreateInput): BusinessUser {
    const user = new BusinessUser({
      ...props,
      firstName: null,
      lastName: null,
      phone: null,
      phoneVerification: getPhoneVerificationNull(),
      emailVerification: getEmailVerificationNull(),
    });
    user.addDomainEvent(new BusinessUserCreatedEvent(user.toDto()));
    return user;
  }

  public update(data: BusinessUserUpdateInput) {
    this.props.firstName = data.firstName;
    this.props.lastName = data.lastName;
    this.props.timezone = data.timezone;
    this.props.lng = data.lng;
  }

  public addPhoneVerificationAttempt(args: { lng: PossibleLngs, timezone: TimeZone | null, newPhone: Phone }): Result2<{
    code: Code,
    attemptsLeft: number,
  }> {
    const { lng, timezone, newPhone } = args;
    const now = Dat.create().value;

    if (this.props.phoneVerification.blockedUntil && this.props.phoneVerification.blockedUntil.t > now.t)
      return Result2.fail([new BusinessUserErrors.NoAttemptsLeft({
        lng,
        blockedUntil: this.props.phoneVerification.blockedUntil,
        timezone,
      })]);


    this.setVerifyingPhone(newPhone);

    if (!this.props.phoneVerification.attempts)
      this.props.phoneVerification.attempts = [now];
    else
      this.props.phoneVerification.attempts.push(now);

    const _24hrsAgo = Dat.create({ value: now.l.minus({ hours: 24 }) }).value;
    const inLast24hrs = this.props.phoneVerification.attempts?.filter(
      (attempt) => attempt.t > _24hrsAgo.t,
    );
    const attemptsLeft = BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - (inLast24hrs?.length || 0);

    if (attemptsLeft < 1) {
      this.props.phoneVerification.blockedUntil = Dat.create({ value: now.l.plus({ hours: 24 }).startOf('minute') }).value;
      this.props.phoneVerification.attempts = null;
    }

    return Result2.ok({
      code: this.props.phoneVerification.code!,
      attemptsLeft,
    });
  }

  private setVerifyingPhone(newPhone: Phone) {
    this.props.phoneVerification.status = VerificationStatus.STARTED;
    this.props.phoneVerification.newPhone = newPhone;
    this.props.phoneVerification.code = Code.getRandom();
    const now = Dat.create().value;
    this.props.phoneVerification.codeExpiresAt = DatSec.create({ value: now.l.plus({ minutes: 1 }) }).value;
  }

  public confirmPhoneCode(args: { code: Code, lng: PossibleLngs }): Result2<void> {
    const { code, lng } = args;
    const now = Dat.create().value;

    // Check if there's verification in progress
    if (this.props.phoneVerification.status !== VerificationStatus.STARTED)
      return Result2.fail([new BusinessUserErrors.NoVerificationInProgress({ lng })]);

    if (!this.props.phoneVerification.code ||
        !this.props.phoneVerification.newPhone ||
        !this.props.phoneVerification.codeExpiresAt)
      throw Error('Phone verification started but code or new phone is missing');

    if (this.props.phoneVerification.codeExpiresAt.t < now.t) {
      this.props.phoneVerification.status = VerificationStatus.CODE_EXPIRED;
      return Result2.fail([new BusinessUserErrors.CodeExpired({ lng })]);
    }


    if (!this.props.phoneVerification.code.equals(code)) {
      this.props.phoneVerification.status = VerificationStatus.INVALID_CODE;
      return Result2.fail([new BusinessUserErrors.InvalidCode({ lng })]);
    }

    this.props.phoneVerification.status = VerificationStatus.SUCCESS;
    this.props.phone = this.props.phoneVerification.newPhone;

    return Result2.ok(undefined);
  }

  public addEmailVerificationAttempt(args: { lng: PossibleLngs, timezone: TimeZone | null, newEmail: Email }): Result2<{
    code: Code,
    attemptsLeft: number,
  }> {
    const { lng, timezone, newEmail } = args;
    const now = Dat.create().value;

    if (this.props.emailVerification.blockedUntil && this.props.emailVerification.blockedUntil.t > now.t)
      return Result2.fail([new BusinessUserErrors.NoAttemptsLeft({
        lng,
        blockedUntil: this.props.emailVerification.blockedUntil,
        timezone,
      })]);


    this.setVerifyingEmail(newEmail);

    if (!this.props.emailVerification.attempts)
      this.props.emailVerification.attempts = [now];
    else
      this.props.emailVerification.attempts.push(now);

    const _24hrsAgo = Dat.create({ value: now.l.minus({ hours: 24 }) }).value;
    const inLast24hrs = this.props.emailVerification.attempts?.filter(
      (attempt) => attempt.t > _24hrsAgo.t,
    );
    const attemptsLeft = BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - (inLast24hrs?.length || 0);

    if (attemptsLeft < 1) {
      this.props.emailVerification.blockedUntil = Dat.create({ value: now.l.plus({ hours: 24 }).startOf('minute') }).value;
      this.props.emailVerification.attempts = null;
    }

    return Result2.ok({
      code: this.props.emailVerification.code!,
      attemptsLeft,
    });
  }

  private setVerifyingEmail(newEmail: Email) {
    this.props.emailVerification.status = VerificationStatus.STARTED;
    this.props.emailVerification.newEmail = newEmail;
    this.props.emailVerification.code = Code.getRandom();
    const now = Dat.create().value;
    this.props.emailVerification.codeExpiresAt = DatSec.create({ value: now.l.plus({ minutes: 1 }) }).value;
  }

  public confirmEmailCode(args: { code: Code, lng: PossibleLngs }): Result2<void> {
    const { code, lng } = args;
    const now = Dat.create().value;

    // Check if there's verification in progress
    if (this.props.emailVerification.status !== VerificationStatus.STARTED)
      return Result2.fail([new BusinessUserErrors.NoVerificationInProgress({ lng })]);

    if (!this.props.emailVerification.code ||
        !this.props.emailVerification.newEmail ||
        !this.props.emailVerification.codeExpiresAt)
      throw Error('Email verification started but code or new email is missing');

    if (this.props.emailVerification.codeExpiresAt.t < now.t) {
      this.props.emailVerification.status = VerificationStatus.CODE_EXPIRED;
      return Result2.fail([new BusinessUserErrors.CodeExpired({ lng })]);
    }


    if (!this.props.emailVerification.code.equals(code)) {
      this.props.emailVerification.status = VerificationStatus.INVALID_CODE;
      return Result2.fail([new BusinessUserErrors.InvalidCode({ lng })]);
    }

    this.props.emailVerification.status = VerificationStatus.SUCCESS;
    this.props.email = this.props.emailVerification.newEmail;

    return Result2.ok(undefined);
  }

  public toDto(): BusinessUserDto {
    const { firstName, lastName, email, phone, timezone, lng, phoneVerification, emailVerification, ...rest } = this.props;

    return {
      ...rest,
      id: this.id.toString(),
      firstName: firstName? firstName.value : null,
      lastName: lastName? lastName.value : null,
      lng: lng.value,
      email: email.value,
      phone: phone?.value || null,
      timezone: timezone.value,
      phoneVerification: {
        ...phoneVerification,
        attempts: phoneVerification.attempts?.map(
          (attempt) => attempt.s,
        ) || null,
        blockedUntil: phoneVerification.blockedUntil?.s || null,
        codeExpiresAt: phoneVerification.codeExpiresAt?.s || null,
        newPhone: phoneVerification.newPhone?.value || null,
        code: phoneVerification.code?.value || null,
      },
      emailVerification: {
        ...emailVerification,
        attempts: emailVerification.attempts?.map(
          (attempt) => attempt.s,
        ) || null,
        blockedUntil: emailVerification.blockedUntil?.s || null,
        codeExpiresAt: emailVerification.codeExpiresAt?.s || null,
        newEmail: emailVerification.newEmail?.value || null,
        code: emailVerification.code?.value || null,
      },
    };
  }

  public static assemble(dto: BusinessUserDto): BusinessUser {
    const { id, firstName, lastName, email, timezone, phone, lng, phoneVerification, emailVerification, ...rest } = dto;

    const phoneVerificationStatus = phoneVerification.status;
    if (!isVerificationStatus(phoneVerificationStatus))
      throw Error(`Invalid phone verification status ${phoneVerification.status}`);

    const emailVerificationStatus = emailVerification.status;
    if (!isVerificationStatus(emailVerificationStatus))
      throw Error(`Invalid email verification status ${emailVerification.status}`);

    return new BusinessUser(
      {
        ...rest,
        firstName: firstName? Name.create(firstName).value : null,
        lastName: lastName? Name.create(lastName).value : null,
        lng: UserLng.create({ value: lng }).value,
        email: Email.create(email).value,
        timezone: TimeZone.create(timezone).value,
        phone: phone ? Phone.create({ value: phone }).value : null,
        phoneVerification: {
          ...phoneVerification,
          status: phoneVerificationStatus,
          attempts: phoneVerification.attempts?.length ?
            phoneVerification.attempts.map(
              (attempt) => Dat.create({ value: attempt }).value
            ) as [Dat, ...Dat[]]
            : null,
          blockedUntil: phoneVerification.blockedUntil ? Dat.create({ value: phoneVerification.blockedUntil }).value : null,
          codeExpiresAt: phoneVerification.codeExpiresAt ? DatSec.create({ value: phoneVerification.codeExpiresAt }).value : null,
          newPhone: phoneVerification.newPhone ? Phone.create({ value: phoneVerification.newPhone }).value : null,
          code: phoneVerification.code ? Code.create({ value: phoneVerification.code, lng: getLng(lng) }).value : null,
        },
        emailVerification: {
          ...emailVerification,
          status: emailVerificationStatus,
          attempts: emailVerification.attempts?.length ?
            emailVerification.attempts.map(
              (attempt) => Dat.create({ value: attempt }).value
            ) as [Dat, ...Dat[]]
            : null,
          blockedUntil: emailVerification.blockedUntil ? Dat.create({ value: emailVerification.blockedUntil }).value : null,
          codeExpiresAt: emailVerification.codeExpiresAt ? DatSec.create({ value: emailVerification.codeExpiresAt }).value : null,
          newEmail: emailVerification.newEmail ? Email.create(emailVerification.newEmail).value : null,
          code: emailVerification.code ? Code.create({ value: emailVerification.code, lng: getLng(lng) }).value : null,
        },
      },
      new EntityID(id),
    );
  }

  public static forFrontend(dto: BusinessUserDto): BusinessUserForFrontend {
    const { phoneVerification, emailVerification, ...rest } = dto;
    return {
      ...rest,
      phoneVerification: { // code and attempts are hidden from the frontend
        status: phoneVerification.status,
        blockedUntil: phoneVerification.blockedUntil,
        codeExpiresAt: phoneVerification.codeExpiresAt,
        newPhone: phoneVerification.newPhone,
      },
      emailVerification: { // code and attempts are hidden from the frontend
        status: emailVerification.status,
        blockedUntil: emailVerification.blockedUntil,
        codeExpiresAt: emailVerification.codeExpiresAt,
        newEmail: emailVerification.newEmail,
      },
    };
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isVerificationStatus(test: any): test is VerificationStatus {
  return test && Object.keys(VerificationStatus).includes(test);
}
