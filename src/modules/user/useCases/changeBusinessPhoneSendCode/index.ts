import { ChangeBusinessPhoneSendCode } from './ChangeBusinessPhoneSendCode';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
import { GuardUuid } from '@shared/decorators/GuardUuid';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });

const repo = new BusinessUserRepo(models.BusinessUser);
const controller = new ChangeBusinessPhoneSendCode({
  repo,
  contextProvider,
});

const decorated1 = new GuardUuid({ controller, uuids: ['id'] });
const decorated2 = new ReturnUnexpectedError({
  wrapee: decorated1,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated2.execute.bind(decorated2);
